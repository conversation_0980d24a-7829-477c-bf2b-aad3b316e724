import { current<PERSON>pan, <PERSON><PERSON>, NOOP_SPAN, Score<PERSON><PERSON>mary, Span } from "braintrust";
import { PROJECT_NAME } from "./meta-eval";
import { makeEvalChat, SpanChatLogger } from "./eval-vars";
import { ChatMetrics } from "../llm/chat";
import { ALL_TOOL_NAMES } from "../tools";
import {
  scoreCorrectTotalRowsGenerated,
  scoreToolUsage,
  scoreNovelty,
  scoreDistributionalAlignment,
  scoreSemanticConsistency,
  scoreDiversity,
  scoreEdgeCaseCoverage,
} from "./synthetic-data-gen-scorer";

export interface SyntheticDataGenResult {
  updated: Record<string, ScoreSummary>;
  metrics: ChatMetrics;
  numberOfRowsToGenerate: number;
}

export const NUMBER_OF_ROWS_TO_GENERATE = 5;

//This eval is used to test the quality of the synthetic data generated by the model in various ways.
//Scoring high on certain metrics does not necessarily means it's absolutely better because users might want different modes of generation.
Eval(PROJECT_NAME, {
  data: [
    //These are checking if <PERSON> understands different ways of asking for row generation.
    {
      input: `Generate ${NUMBER_OF_ROWS_TO_GENERATE} rows for this dataset.`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE,
      },
    },
    {
      input: `Generate ${NUMBER_OF_ROWS_TO_GENERATE} dataset rows.`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE,
      },
    },
    {
      input: `Generate ${NUMBER_OF_ROWS_TO_GENERATE} new rows`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE,
      },
    },
    {
      input: `add ${NUMBER_OF_ROWS_TO_GENERATE} new rows`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE,
      },
    },
    {
      input: `Create ${NUMBER_OF_ROWS_TO_GENERATE} new rows `,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE,
      },
    },
    //Check how loop handles large number of rows generated.
    /*  {
      input: `Generate ${NUMBER_OF_ROWS_TO_GENERATE * 18} new rows`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE * 18,
      },
    },
    {
      input: `Generate ${NUMBER_OF_ROWS_TO_GENERATE * 2 + 4} new rows`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE * 2 + 4,
      },
    }, */
    //These are checking how Loop performs on different modalities of generation.
    {
      input: `Generate ${NUMBER_OF_ROWS_TO_GENERATE} new rows that cover edge and corner cases`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE,
      },
    },
    /*  {
      input: `Generate ${NUMBER_OF_ROWS_TO_GENERATE} new rows that are similar to the existing rows but with slight variations`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE,
      },
    },
    {
      input: `Look at the dataset and generate ${NUMBER_OF_ROWS_TO_GENERATE} new rows that follow the same format and same pattern but introduces new data with slight variations.`,
      metadata: {
        numberOfRowsToGenerate: NUMBER_OF_ROWS_TO_GENERATE,
      },
    }, */
  ],
  task: async (input, { metadata }) => {
    let consoleLogger: SpanChatLogger | undefined = undefined;
    let rawOutput: Span = NOOP_SPAN;
    try {
      const {
        chat,
        consoleLogger: cl,
        rawOutput: ro,
        tools,
      } = await makeEvalChat("movie-matcher", {
        //These are the only tools available in dataset page
        allowed_tools: ALL_TOOL_NAMES.filter(
          (tool) => tool === "edit_data" || tool === "get_results",
        ),
        applicationContext: "dataset",
      });
      consoleLogger = cl;
      rawOutput = ro;

      //dataset before any edits
      const initial_dataset = await tools.tools.get_results(
        {
          index: 0,
          numSamples: 999,
        },
        currentSpan(),
      );

      await chat.turn(input);

      //dataset after edits
      const final_dataset = await tools.tools.get_results(
        {
          index: 0,
          numSamples: 999,
        },
        currentSpan(),
      );

      const synthetic_dataset = final_dataset.filter(
        (row) =>
          row.metadata &&
          typeof row.metadata === "object" &&
          "synthetic" in row.metadata &&
          row.metadata.synthetic,
      );

      const { toolCallsByType: _, ...numericMetrics } = chat.metrics;
      currentSpan().log({ metrics: numericMetrics });
      return {
        synthetic_dataset: synthetic_dataset,
        initial_dataset: initial_dataset,
        metrics: chat.metrics,
        numberOfRowsToGenerate: metadata.numberOfRowsToGenerate,
      };
    } finally {
      await consoleLogger?.flush();
      rawOutput.end();
    }
  },
  scores: [
    scoreCorrectTotalRowsGenerated,
    scoreToolUsage,
    scoreNovelty,
    scoreDistributionalAlignment,
    scoreSemanticConsistency,
    scoreDiversity,
    scoreEdgeCaseCoverage,
  ],
});
