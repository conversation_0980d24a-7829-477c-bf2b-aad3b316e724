import { Score, LLMClassifierFromTemplate } from "autoevals";
import { GetResultsToolResult } from "../tools";

// You want the model to generate the correct number of rows.
export function scoreCorrectTotalRowsGenerated({
  output,
}: {
  output: {
    synthetic_dataset: GetResultsToolResult[];
    initial_dataset: GetResultsToolResult[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    metrics: any;
    numberOfRowsToGenerate: number;
  };
}): Score {
  const syntheticCount = output.synthetic_dataset?.length || 0;
  return {
    name: "correct_total_rows_generated",
    score: syntheticCount === output.numberOfRowsToGenerate ? 1 : 0,
    metadata: {
      generated: syntheticCount,
      expected: output.numberOfRowsToGenerate,
    },
  };
}

//You want the model to use both get results and edit data because it should know about the original data before generating the synthetic data.
export function scoreToolUsage({
  output,
}: {
  output: {
    synthetic_dataset: GetResultsToolResult[];
    initial_dataset: GetResultsToolResult[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    metrics: any;
  };
}): Score {
  const editDataCalls = output.metrics?.toolCallsByType?.edit_data;
  const getResultsCalls = output.metrics?.toolCallsByType?.get_results;

  // Calculate expected edit_data calls based on batches of 10
  const syntheticCount = output.synthetic_dataset?.length || 0;
  const expectedEditCalls = Math.ceil(syntheticCount / 10);

  // Perfect score if both tools are used correctly
  const score =
    editDataCalls === expectedEditCalls && getResultsCalls === 1
      ? 1
      : editDataCalls > expectedEditCalls || editDataCalls < expectedEditCalls
        ? 0.5 // Good if edit_data calls are correct but get_results might be missing
        : 0;

  return {
    name: "tool_usage_efficiency",
    score: score,
    metadata: {
      editDataCalls,
      idealEditCalls: expectedEditCalls,
      getResultsCalls,
      idealGetResultsCalls: 1,
      syntheticRowsGenerated: syntheticCount,
      batchSize: 10,
    },
  };
}

// Helper function to compute text similarity using simple token overlap
function tokenOverlapSimilarity(text1: string, text2: string): number {
  const tokens1 = new Set(text1.toLowerCase().split(/\s+/));
  const tokens2 = new Set(text2.toLowerCase().split(/\s+/));

  const intersection = new Set([...tokens1].filter((x) => tokens2.has(x)));
  const union = new Set([...tokens1, ...tokens2]);

  return union.size > 0 ? intersection.size / union.size : 0;
}

// Helper function to compute object similarity
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function objectSimilarity(obj1: any, obj2: any): number {
  const str1 = JSON.stringify(obj1, null, 0);
  const str2 = JSON.stringify(obj2, null, 0);
  return tokenOverlapSimilarity(str1, str2);
}

// Check for creativity (the synthetic row differs from anything that existed before
export function scoreNovelty({
  output,
}: {
  output: {
    synthetic_dataset: GetResultsToolResult[];
    initial_dataset: GetResultsToolResult[];
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
    metrics: any;
  };
}): Score {
  if (!output.synthetic_dataset?.length) {
    return {
      name: "novelty",
      score: 0,
      error: "No synthetic data generated",
    };
  }

  const allOriginalRows = output.initial_dataset || [];
  let totalNoveltyScore = 0;
  const noveltyDetails: string[] = [];

  for (const syntheticRow of output.synthetic_dataset) {
    // Find the most similar row in the original dataset
    let maxSimilarity = 0;

    for (const originalRow of allOriginalRows) {
      const inputSim = objectSimilarity(syntheticRow.input, originalRow.input);
      const expectedSim = objectSimilarity(
        syntheticRow.expected,
        originalRow.expected,
      );
      const similarity = (inputSim + expectedSim) / 2;
      maxSimilarity = Math.max(maxSimilarity, similarity);
    }

    // Novelty is inverse of similarity
    const novelty = 1 - maxSimilarity;
    totalNoveltyScore += novelty;

    if (maxSimilarity > 0.9) {
      noveltyDetails.push(
        `Row too similar (${(maxSimilarity * 100).toFixed(1)}% match)`,
      );
    }
  }

  const avgNovelty = totalNoveltyScore / output.synthetic_dataset.length;

  return {
    name: "novelty",
    score: avgNovelty,
    metadata: {
      averageNovelty: avgNovelty,
      issues: noveltyDetails,
    },
  };
}

//In certain cases, you want the synthetic data to be distributed similarly to the original data.
export function scoreDistributionalAlignment({
  output,
}: {
  output: {
    synthetic_dataset: GetResultsToolResult[];
    initial_dataset: GetResultsToolResult[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    metrics: any;
  };
}): Score {
  if (!output.synthetic_dataset?.length || !output.initial_dataset?.length) {
    return {
      name: "distributional_alignment",
      score: 0,
      error: "Missing data for comparison",
    };
  }

  // Extract categorical values from expected fields
  const getCategories = (dataset: GetResultsToolResult[]) => {
    const categories: string[] = [];
    for (const row of dataset) {
      if (typeof row.expected === "string") {
        categories.push(row.expected);
      } else if (
        typeof row.expected === "object" &&
        row.expected !== null &&
        "label" in row.expected
      ) {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
        categories.push((row.expected as any).label);
      }
    }
    return categories;
  };

  const originalCategories = getCategories(output.initial_dataset);
  const syntheticCategories = getCategories(output.synthetic_dataset);

  // Calculate distributions
  const getDistribution = (categories: string[]) => {
    const counts: Record<string, number> = {};
    for (const cat of categories) {
      counts[cat] = (counts[cat] || 0) + 1;
    }
    const total = categories.length;
    const dist: Record<string, number> = {};
    for (const [cat, count] of Object.entries(counts)) {
      dist[cat] = count / total;
    }
    return dist;
  };

  const origDist = getDistribution(originalCategories);
  const synthDist = getDistribution(syntheticCategories);

  // Calculate KL divergence (simplified)
  let klDivergence = 0;
  const allCategories = new Set([
    ...Object.keys(origDist),
    ...Object.keys(synthDist),
  ]);

  for (const cat of allCategories) {
    const p = origDist[cat] || 0.001; // Add small epsilon to avoid log(0)
    const q = synthDist[cat] || 0.001;
    if (p > 0) {
      klDivergence += p * Math.log(p / q);
    }
  }

  // Convert KL divergence to a score (0-1, where 1 is perfect alignment)
  const score = Math.exp(-klDivergence);

  return {
    name: "distributional_alignment",
    score: score,
    metadata: {
      klDivergence,
      originalDistribution: origDist,
      syntheticDistribution: synthDist,
    },
  };
}

//In many cases, you ideally want semantic consistency between the original data and the synthetic data or else they'd ruin your evaluation.
export async function scoreSemanticConsistency({
  output,
}: {
  output: {
    synthetic_dataset: GetResultsToolResult[];
    initial_dataset: GetResultsToolResult[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    metrics: any;
  };
}): Promise<Score> {
  if (!output.synthetic_dataset?.length) {
    return {
      name: "semantic_consistency",
      score: 0,
      error: "No synthetic data generated",
    };
  }

  const samplesToEvaluate = output.synthetic_dataset.slice(0, 5);

  const originalExamples = output.initial_dataset.slice(
    0,
    Math.min(5, output.initial_dataset.length),
  );

  const formattedOriginals = originalExamples
    .map(
      (row, i) =>
        `Example ${i + 1}:\nInput: ${JSON.stringify(row.input)}\nExpected: ${JSON.stringify(row.expected)}`,
    )
    .join("\n\n");

  const formattedSynthetic = samplesToEvaluate
    .map(
      (row, i) =>
        `Row ${i + 1}:\nInput: ${JSON.stringify(row.input)}\nExpected: ${JSON.stringify(row.expected)}`,
    )
    .join("\n\n");

  const formattedOutput = `Here are reference examples from the ORIGINAL dataset:\n${formattedOriginals}\n\nNow evaluate the following SYNTHETIC rows:\n${formattedSynthetic}`;

  return await SemanticConsistencyClassifier({
    output: formattedOutput,
    expected: "",
  });
}

const SemanticConsistencyClassifier = LLMClassifierFromTemplate({
  name: "Semantic Consistency Classifier",
  model: "claude-3-5-sonnet-latest",
  promptTemplate: `
You are an expert evaluator assessing the semantic consistency between an \`input\` and its corresponding \`expected\` answer.

You will receive two blocks of data:
1. **REFERENCE examples from the ORIGINAL dataset** – these show *correct* input→expected mappings.
2. **SYNTHETIC rows** – newly generated examples that must follow the same mapping.

Your job is to decide whether, *row‐for‐row*, each synthetic \`expected\` is a semantically correct response to its \`input\` **given the pattern illustrated by the reference examples**.

Guidelines when judging a synthetic pair:
• If the expected text directly and fully answers or transforms the input in the same way the references do → consistent.
• Minor wording/style variations that do not change meaning are acceptable.
• If the expected text is unrelated, incomplete, contradictory, or otherwise wrong → inconsistent.

After reviewing all synthetic rows, pick ONE overall rating:
(a) **Excellent** – ≥90 % of rows are consistent.
(b) **Good** – 70–89 % of rows are consistent.
(c) **Poor** – <70 % of rows are consistent.

Return ONLY the letter **a**, **b**, or **c**.

Here is the data (reference block first, synthetic block second):

{{{output}}}
`,
  choiceScores: {
    a: 1,
    b: 0.7,
    c: 0.3,
  },
});

//Synthetic data should ideally have diversity or else it means the LLM just generated rows that are similar to each other.
export function scoreDiversity({
  output,
}: {
  output: {
    synthetic_dataset: GetResultsToolResult[];
    initial_dataset: GetResultsToolResult[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    metrics: any;
  };
}): Score {
  if (
    !output.synthetic_dataset?.length ||
    output.synthetic_dataset.length < 2
  ) {
    return {
      name: "diversity",
      score: 0,
      error: "Not enough synthetic data to measure diversity",
    };
  }

  // Calculate pairwise distances between all synthetic rows
  const distances: number[] = [];

  for (let i = 0; i < output.synthetic_dataset.length; i++) {
    for (let j = i + 1; j < output.synthetic_dataset.length; j++) {
      const row1 = output.synthetic_dataset[i];
      const row2 = output.synthetic_dataset[j];

      const inputSim = objectSimilarity(row1.input, row2.input);
      const expectedSim = objectSimilarity(row1.expected, row2.expected);
      const similarity = (inputSim + expectedSim) / 2;

      // Distance is inverse of similarity
      distances.push(1 - similarity);
    }
  }

  const avgDistance = distances.reduce((a, b) => a + b, 0) / distances.length;

  // Check for near-duplicates
  const nearDuplicates = distances.filter((d) => d < 0.1).length;

  return {
    name: "diversity",
    score: avgDistance,
    metadata: {
      averageDistance: avgDistance,
      nearDuplicatePairs: nearDuplicates,
      totalPairs: distances.length,
    },
  };
}

//Synthetic data covering edge case could be nice -- it doesn't necessarily need to score high on this but wanted to keep track.
const EdgeCaseCoverageClassifier = LLMClassifierFromTemplate({
  name: "Edge Case Coverage Classifier",
  model: "claude-3-5-sonnet-latest",
  promptTemplate: `
You are an expert evaluator assessing whether synthetic data rows represent edge cases or corner cases compared to typical examples.

Edge cases might include:
- Unusual input lengths or formats
- Boundary values (very large/small numbers, empty strings, special characters)
- Rare combinations or patterns
- Complex reasoning requirements
- Ambiguous or challenging scenarios
- Error conditions or invalid inputs

{{{output}}}

Analyze the synthetic rows and classify the edge case coverage as:

(a) Excellent - Many synthetic rows (50%+) represent meaningful edge cases
(b) Good - Some synthetic rows (20-49%) represent edge cases
(c) Poor - Few or no synthetic rows (<20%) represent edge cases

Consider whether the synthetic data explores boundaries, unusual cases, or scenarios not well-represented in the original data.
`,
  choiceScores: {
    a: 1,
    b: 0.6,
    c: 0.2,
  },
});

export async function scoreEdgeCaseCoverage({
  output,
}: {
  output: {
    synthetic_dataset: GetResultsToolResult[];
    initial_dataset: GetResultsToolResult[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    metrics: any;
  };
}): Promise<Score> {
  if (!output.synthetic_dataset?.length) {
    return {
      name: "edge_case_coverage",
      score: 0,
      error: "No synthetic data generated",
    };
  }

  // Format both original and synthetic data for the prompt
  const originalSample = output.initial_dataset
    .slice(0, 5)
    .map(
      (row, i) =>
        `Row ${i + 1}:\nInput: ${JSON.stringify(row.input)}\nExpected: ${JSON.stringify(row.expected)}`,
    )
    .join("\n\n");

  const syntheticRows = output.synthetic_dataset
    .map(
      (row, i) =>
        `Row ${i + 1}:\nInput: ${JSON.stringify(row.input)}\nExpected: ${JSON.stringify(row.expected)}`,
    )
    .join("\n\n");

  const formattedOutput = `Original dataset sample (for reference):
${originalSample}

Synthetic rows to evaluate:
${syntheticRows}`;

  return await EdgeCaseCoverageClassifier({
    output: formattedOutput,
    expected: "", // Not used in our template but required by the type
  });
}
