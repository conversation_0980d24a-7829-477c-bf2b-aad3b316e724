#!/usr/bin/env python3
import argparse
import sys

import boto3
from botocore.exceptions import ClientError


def get_instances(stack_name_filter=None, name_filter=None):
    """Get EC2 instances, optionally filtered by CloudFormation stack name and/or name text."""
    ec2 = boto3.client("ec2")

    try:
        # Get all instances
        response = ec2.describe_instances()

        instances = []
        for reservation in response["Reservations"]:
            for instance in reservation["Instances"]:
                # Only include running instances
                if instance["State"]["Name"] != "running":
                    continue

                instance_id = instance["InstanceId"]
                instance_name = "-"

                # Get instance name from tags
                if "Tags" in instance:
                    for tag in instance["Tags"]:
                        if tag["Key"] == "Name":
                            instance_name = tag["Value"]
                            break

                # Check if we need to filter by stack name
                if stack_name_filter:
                    stack_name = None
                    if "Tags" in instance:
                        for tag in instance["Tags"]:
                            if tag["Key"] == "aws:cloudformation:stack-name":
                                stack_name = tag["Value"]
                                break

                    # Only include instances that match the stack name filter
                    if stack_name != stack_name_filter:
                        continue

                # Check if we need to filter by name text
                if name_filter and name_filter.lower() not in instance_name.lower():
                    continue

                instances.append((instance_id, instance_name))

        return instances

    except ClientError as e:
        print(f"Error getting instances: {e}")
        sys.exit(1)


def resolve_stack_alias(alias):
    """Resolve special aliases to their corresponding stack names."""
    aliases = {"prod": "bt3subnets-3", "staging": "bt-staging"}
    return aliases.get(alias, alias)


def main():
    parser = argparse.ArgumentParser(
        description="List EC2 instances with optional CloudFormation stack filtering",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Stack Aliases:
    prod = bt3subnets-3
    staging = bt-staging

Examples:
  ec2-list.py
  ec2-list.py prod
  ec2-list.py staging
  ec2-list.py bt3subnets-3
  ec2-list.py -n Writer
  ec2-list.py prod -n Writer
        """,
    )

    parser.add_argument(
        "stack_name", nargs="?", help="CloudFormation stack name to filter by (or 'prod'/'staging' aliases)"
    )

    parser.add_argument("-n", "--name", help="Filter instances by text in the name (case-insensitive substring match)")

    args = parser.parse_args()

    # Resolve stack name alias if provided
    stack_name_filter = None
    if args.stack_name:
        stack_name_filter = resolve_stack_alias(args.stack_name)

    # Get instances
    instances = get_instances(stack_name_filter, args.name)

    # Sort instances by name
    instances.sort(key=lambda x: x[1])

    # Print instances
    for instance_id, instance_name in instances:
        print(f"{instance_id}\t{instance_name}")


if __name__ == "__main__":
    main()
