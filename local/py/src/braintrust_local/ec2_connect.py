#!/usr/bin/env python3
import argparse
import os
import subprocess
import sys
from pathlib import Path

import boto3
from botocore.exceptions import ClientError


def get_instance_az(instance_id):
    """Get the Availability Zone for an EC2 instance."""
    ec2 = boto3.client("ec2")
    try:
        response = ec2.describe_instances(InstanceIds=[instance_id])
        reservations = response["Reservations"]

        # Check if the instance exists
        if not reservations or not reservations[0].get("Instances"):
            print(f"No instance found with ID {instance_id}.")
            sys.exit(1)

        # Get AZ from the first reservation's first instance
        az = reservations[0]["Instances"][0]["Placement"]["AvailabilityZone"]
        return az
    except ClientError as e:
        print(f"Error getting instance AZ: {e}")
        sys.exit(1)


def parse_target(target):
    """Parse user@instance-id format and return user and instance_id."""
    if "@" in target:
        user, instance_id = target.split("@", 1)
        return user, instance_id
    else:
        # Default to ubuntu if no user specified
        return "ubuntu", target


def connect_to_instance(target, user_key_path=None, remote_command=None):
    """Connect to an EC2 instance using SSH with ProxyCommand."""
    os_user, instance_id = parse_target(target)
    home_dir = Path.home()

    # Determine which private/public key pair to use
    if user_key_path:
        private_key_path = Path(user_key_path)
        public_key_path = Path(str(private_key_path) + ".pub")
    else:
        possible_keys = [home_dir / ".ssh/id_ed25519", home_dir / ".ssh/id_rsa"]
        private_key_path = None
        public_key_path = None
        for pk in possible_keys:
            if pk.exists():
                private_key_path = pk
                public_key_path = Path(str(pk) + ".pub")
                break

    if not private_key_path or not private_key_path.exists():
        print("No valid SSH key found or provided.")
        sys.exit(1)

    if not public_key_path or not public_key_path.exists():
        print("Public key not found.")
        sys.exit(1)

    print(f"Using private key: {private_key_path}")
    print(f"Using public key: {public_key_path}")

    # Get instance AZ
    az = get_instance_az(instance_id)

    # Initialize EC2 Instance Connect client
    ec2_instance_connect = boto3.client("ec2-instance-connect")

    try:
        with open(public_key_path, "r") as f:
            public_key = f.read()

        print(f"Sending SSH key to instance {os_user}@{instance_id}")

        ec2_instance_connect.send_ssh_public_key(
            InstanceId=instance_id, InstanceOSUser=os_user, SSHPublicKey=public_key, AvailabilityZone=az
        )

        print(f"Connecting to instance {os_user}@{instance_id}")

        # Use SSH with ProxyCommand for EC2 Instance Connect tunnel
        ssh_cmd = [
            "ssh",
            "-i",
            str(private_key_path),
            "-o",
            "StrictHostKeyChecking=no",
            "-o",
            f"ProxyCommand=aws ec2-instance-connect open-tunnel --instance-id {instance_id}",
            f"{os_user}@{instance_id}",
        ]

        # Add remote command if provided
        if remote_command:
            ssh_cmd.append(remote_command)

        os.execvp("ssh", ssh_cmd)

    except FileNotFoundError:
        print("SSH key files not found.")
        sys.exit(1)
    except ClientError as e:
        print(f"AWS API error: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Connect to an EC2 instance using SSH with EC2 Instance Connect",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  ec2-connect.py ubuntu@i-1234567890abcdef0
  ec2-connect.py -i ~/.ssh/my-key.pem ubuntu@i-1234567890abcdef0
  ec2-connect.py -i ~/.ssh/my-key.pem ubuntu@i-1234567890abcdef0 'whoami'
        """,
    )

    parser.add_argument(
        "-i", "--identity-file", help="Path to the private key file (default: auto-detect from ~/.ssh/)"
    )

    parser.add_argument(
        "target", help="Target in format user@instance-id or just instance-id (defaults to ubuntu user)"
    )

    parser.add_argument("command", nargs="?", help="Command to execute on the remote instance")

    args = parser.parse_args()

    connect_to_instance(args.target, args.identity_file, args.command)


if __name__ == "__main__":
    main()
