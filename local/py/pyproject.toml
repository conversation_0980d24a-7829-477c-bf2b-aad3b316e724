[project]
name = "braintrust_local"
version = "0.0.1"
dependencies = [
    "migra[pg]==3.0.1663481299",
    "psycopg2-binary==2.9.10",
    "clickhouse-connect==0.7.3",
    "tabulate==0.9.0",
    "boto3",
]

[project.scripts]
generate-migration = "braintrust_local.generate_migration:main"
app-db-update-foreign-keys-enable-cascade-delete = "braintrust_local.app_db_util:update_foreign_keys_enable_cascade_delete"
app-db-update-foreign-keys-disable-cascade-delete = "braintrust_local.app_db_util:update_foreign_keys_disable_cascade_delete"
print-api-db-url = "braintrust_local.api_db_util:print_api_db_url"
print-app-db-url = "braintrust_local.app_db_util:print_app_db_url"
generate-docker-compose = "braintrust_local.generate_docker_compose:main"
doc-snippets = "braintrust_local.doc_snippets:main"
btapi = "braintrust_local.btapi:main"
assume = "braintrust_local.aws_assume:main"
ec2-connect = "braintrust_local.ec2_connect:main"
ec2-list = "braintrust_local.ec2_list:main"
ecs-connect = "braintrust_local.ecs_connect:main"
flaky-tests-report = "braintrust_local.flaky_tests:print_flaky_tests_report"
