import { Request, Response } from "express";
import { assertBrainstoreEnabled, runBrainstore } from "./brainstore";
import { authCheckObjectIds, authCheckTrackedObjects } from "./auth";
import { getPG } from "../db/pg";
import { z } from "zod";
import { getRequestContext } from "../request_context";
import {
  BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
  getWasCachedToken,
  isEmpty,
  wrapZodError,
} from "../util";
import { sql } from "@braintrust/btql/planner";
import { getTrackedObjectStatus, setIsBackfilling } from "./backfill";
import {
  backfillableObjectTypeSchema,
  LastIndexOperationWithObjectId,
  lastIndexOperationWithObjectId,
  ObjectInfo,
  objectInfoSchema,
  ObjectInfoWithBackfillStatus,
} from "@braintrust/local/app-schema";
import { signalEtl } from "../event_publisher";
import { PENDING_FLUSHABLES } from "../pending_flushables";
import { brainstoreSegmentInfoSchema } from "@braintrust/local/api-schema";

// TODO:
// * Add audit headers

export const brainstoreTrackingRequestSchema = z.object({
  project_id: z.union([z.string(), z.array(z.string())]),
  object_types: z
    .array(backfillableObjectTypeSchema)
    .optional()
    .describe("If unspecified, all backfillable object types are enabled"),
});

async function brainstoreTrackingRequest(
  req: Request,
  res: Response,
  shouldBackfill: boolean,
) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreTrackingRequestSchema.parse(ctx.data),
  );

  const objectTypes =
    params.object_types ?? backfillableObjectTypeSchema.options;

  const projectIds = Array.isArray(params.project_id)
    ? params.project_id
    : [params.project_id];

  const resolvedTrackedObjects = await authCheckTrackedObjects({
    ctx,
    trackedObjects: objectTypes.flatMap((objectType) =>
      projectIds.map((projectId) => ({
        project_id: projectId,
        object_type: objectType,
      })),
    ),
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  await setIsBackfilling({
    trackedObjects: resolvedTrackedObjects,
    shouldBackfill,
  });
  PENDING_FLUSHABLES.add(signalEtl());
  res.json({ is_backfilling: shouldBackfill });
}

export async function enableTrackingRequest(req: Request, res: Response) {
  return await brainstoreTrackingRequest(req, res, true);
}

export async function disableTrackingRequest(req: Request, res: Response) {
  return await brainstoreTrackingRequest(req, res, false);
}

export async function getObjectInfo(objectId: string): Promise<ObjectInfo> {
  return await runBrainstore({
    path: `/object_info`,
    args: { object_id: objectId },
    schema: objectInfoSchema,
    noLog: true,
    isWrite: true,
  });
}

export async function getObjectInfoRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const objectId = req.params.object_id;

  const ctx = getRequestContext(req);
  const resolvedObjectIds = await authCheckObjectIds({
    objectIds: [objectId],
    ctx,
  });

  const objectInfo = await getObjectInfo(objectId);
  const backfillStatusArr = await getTrackedObjectStatus({
    projectIds: [resolvedObjectIds[0].project_id],
    objectType: resolvedObjectIds[0].object_type,
  });
  const backfillStatus = backfillStatusArr.length
    ? backfillStatusArr[0]
    : {
        backfill_frontier_sequence_id: null,
        backfill_target_sequence_id: null,
        backfill_frontier_sequence_id_2: null,
        backfill_target_sequence_id_2: null,
        completed_initial_backfill_ts: null,
        last_backfilled_ts: null,
        last_backfilled_ts_2: null,
        estimated_progress: null,
        enabled: false,
      };
  const ret: ObjectInfoWithBackfillStatus = {
    ...objectInfo,
    ...backfillStatus,
  };

  res.json(ret);
}

export async function getSegmentInfoRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const segmentId = z.string().uuid().safeParse(req.params.segment_id);
  if (!segmentId.success) {
    res.json({});
    return;
  }

  const ctx = getRequestContext(req);
  const segmentInfo = await (async () => {
    const conn = getPG();
    const { rows } = await conn.query(
      `select
        liveness.segment_id,
        liveness.object_id,
        liveness.is_live,
        liveness.last_written_ts,
        liveness.vacuum_index_last_successful_start_ts,
        metadata.last_compacted_index_meta_xact_id,
        metadata.last_compacted_index_meta_tantivy_meta,
        metadata.minimum_pagination_key,
        metadata.num_rows,
        operation.start as index_operation_start,
        operation.last_updated as index_operation_last_updated,
        operation.operation as index_operation,
        task_info.vacuum_index_info
      from brainstore_global_store_segment_id_to_liveness liveness
      left join brainstore_global_store_segment_id_to_metadata metadata using (segment_id)
      left join brainstore_global_store_segment_id_to_last_index_operation operation using (segment_id)
      left join brainstore_global_store_segment_id_to_task_info task_info using (segment_id)
      where liveness.segment_id = $1`,
      [segmentId.data],
    );
    return rows[0] ? brainstoreSegmentInfoSchema.parse(rows[0]) : null;
  })();

  if (!segmentInfo?.object_id) {
    res.json({});
    return;
  }

  const resolvedObjectIds = await authCheckObjectIds({
    objectIds: [segmentInfo.object_id],
    ctx,
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
    ignoreMissingObjects: true,
  });

  if (resolvedObjectIds.length === 0) {
    res.json({});
  } else if (resolvedObjectIds.length > 1) {
    throw new Error("Multiple objects found for segment");
  } else if (resolvedObjectIds[0].object_id !== segmentInfo.object_id) {
    throw new Error("Object ID mismatch");
  } else {
    res.json(segmentInfo);
  }
}

export const brainstoreOptimizeObjectRequest = z.object({
  object_id: z.string(),
  segment_ids: z.array(z.string()).optional(), // If unspecified, optimize all segments
  recompact: z.boolean().optional(),
});

// Ideally we implement this in a way we can introspect later (e.g. via the processlist), but for now,
// we just kick it off in the background.
export async function optimizeObjectRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreOptimizeObjectRequest.parse(ctx.data),
  );

  await authCheckObjectIds({
    objectIds: [params.object_id],
    ctx,
  });

  const result = await runBrainstore({
    path: `/index/optimize`,
    args: {
      segment_ids: params.segment_ids ?? [],
      object_id: params.object_id,
      all: isEmpty(params.segment_ids),
      recompact: params.recompact,
      async: true,
    },
    schema: z.unknown(), // It returns nothing
    isWrite: true,
  });
  res.json(result);
}

export const activeBackfillOperationsSchema = z.object({
  last_updated_ago_secs: z
    .number()
    .default(600)
    .describe(
      "Only consider operations that have been updated in the last N seconds (defaults to 10 minutes)",
    ),
  org_id: z.string().nullish(),
  project_id: z.string().nullish(),
});

export async function getActiveBackfillOperationsRequest(
  req: Request,
  res: Response,
) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    activeBackfillOperationsSchema.parse(ctx.data),
  );

  const operations = await getActiveBackfillOperations({
    body: params,
  });

  res.json(
    await filterOperationsByAccess({
      operations,
      req,
      params,
    }),
  );
}

async function getActiveBackfillOperations({
  body,
}: {
  body: z.infer<typeof activeBackfillOperationsSchema>;
}) {
  const template = sql`
    select
      op.segment_id,
      op.start,
      op.last_updated,
      (op.operation->>'finished')::boolean as finished,
      (op.operation->>'estimated_progress')::double precision as estimated_progress,
      (op.operation->>'stage') as stage,
      (op.operation->>'error') as error,
      (op.operation->'details') as details,
      liveness.object_id
    from brainstore_global_store_segment_id_to_last_index_operation op
    join brainstore_global_store_segment_id_to_liveness liveness using (segment_id)
    WHERE NOT COALESCE((operation->>'finished')::boolean, false)
      AND last_updated >= NOW() - make_interval(secs => ${body.last_updated_ago_secs})
    ORDER BY start DESC, last_updated DESC
  `;

  const { query, params } = template.toNumericParamQuery();

  const conn = getPG();
  const { rows } = await conn.query(query, params);
  return z.array(lastIndexOperationWithObjectId).parse(rows);
}

export const getSegmentStatusRequestSchema = z.object({
  org_id: z.string().nullish(),
  project_id: z.string().nullish(),
});

export async function getFailedSegmentsRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    getSegmentStatusRequestSchema.parse(ctx.data),
  );

  const operations = await getAllFailedSegments();

  res.json(
    await filterOperationsByAccess({
      operations,
      req,
      params,
    }),
  );
}

async function filterOperationsByAccess({
  operations,
  req,
  params,
}: {
  operations: LastIndexOperationWithObjectId[];
  req: Request;
  params: z.infer<typeof getSegmentStatusRequestSchema>;
}) {
  const ctx = getRequestContext(req);
  const objectIds = Array.from(new Set(operations.map((o) => o.object_id)));
  const resolvedObjectIds = await authCheckObjectIds({
    objectIds,
    ctx,
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
    filterOnAccessDenied: true,
    ignoreMissingObjects: true,
  });

  const allowedObjectIds = new Set(
    resolvedObjectIds
      .filter(
        (o) =>
          (!params.project_id || params.project_id === o.project_id) &&
          (!params.org_id || params.org_id === o.org_id),
      )
      .map((o) => o.object_id),
  );
  return operations.filter((o) => allowedObjectIds.has(o.object_id));
}

// NOTE: We don't have a way of pushing RBAC through this query, so its results have to be filtered
// in the API server code (rather than in the SQL query).
async function getAllFailedSegments() {
  const template = sql`
    select
      op.segment_id,
      op.start,
      op.last_updated,
      (op.operation->>'finished')::boolean as finished,
      (op.operation->>'estimated_progress')::double precision as estimated_progress,
      (op.operation->>'stage') as stage,
      (op.operation->>'error') as error,
      (op.operation->'details') as details,
      liveness.object_id
    from brainstore_global_store_segment_id_to_last_index_operation op
    join brainstore_global_store_segment_id_to_liveness liveness using (segment_id)
    WHERE operation->>'error' is not null
    AND liveness.is_live
    ORDER BY start DESC, last_updated DESC
  `;

  const { query, params } = template.toNumericParamQuery();

  const conn = getPG();
  const { rows } = await conn.query(query, params);
  return z.array(lastIndexOperationWithObjectId).parse(rows);
}
