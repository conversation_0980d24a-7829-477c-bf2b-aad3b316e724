// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`binder corner cases 1`] = `
{
  "filter": null,
  "from": {
    "name": "project",
    "objects": [
      "495d041a-7790-4907-b3c7-20ee864569cc",
    ],
  },
  "select": [
    {
      "alias": "foo",
      "expr": {
        "name": [
          "scores",
          "foo",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "maximum": 1,
              "minimum": 0,
              "type": "number",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
  ],
  "unpivot": [],
}
`;

exports[`binder corner cases 2`] = `
{
  "items": {
    "properties": {
      "foo": {
        "anyOf": [
          {
            "maximum": 1,
            "minimum": 0,
            "type": "number",
          },
          {
            "type": "null",
          },
        ],
      },
    },
    "type": "object",
  },
  "type": "array",
}
`;

exports[`binder corner cases 3`] = `
{
  "dimensions": [],
  "filter": null,
  "from": {
    "name": "project",
    "objects": [
      "495d041a-7790-4907-b3c7-20ee864569cc",
    ],
  },
  "measures": [
    {
      "alias": "a",
      "expr": {
        "args": [
          {
            "left": {
              "name": [
                "metrics",
                "prompt_tokens",
              ],
              "op": "field",
              "type": {
                "anyOf": [
                  {
                    "type": "integer",
                  },
                  {
                    "type": "null",
                  },
                ],
              },
            },
            "op": "add",
            "right": {
              "name": [
                "metrics",
                "completion_tokens",
              ],
              "op": "field",
              "type": {
                "anyOf": [
                  {
                    "type": "integer",
                  },
                  {
                    "type": "null",
                  },
                ],
              },
            },
          },
        ],
        "name": "sum",
        "op": "function",
      },
    },
    {
      "alias": "b",
      "expr": {
        "args": [
          {
            "name": [
              "metrics",
              "tokens",
            ],
            "op": "field",
            "type": {
              "anyOf": [
                {
                  "type": "integer",
                },
                {
                  "type": "null",
                },
              ],
            },
          },
        ],
        "name": "sum",
        "op": "function",
      },
    },
  ],
  "pivot": [],
  "unpivot": [],
}
`;

exports[`binder corner cases 4`] = `
{
  "items": {
    "properties": {
      "a": {
        "type": [
          "integer",
          "null",
        ],
      },
      "b": {
        "type": [
          "integer",
          "null",
        ],
      },
    },
    "type": "object",
  },
  "type": "array",
}
`;

exports[`binder corner cases 5`] = `
{
  "filter": {
    "left": {
      "name": [
        "metadata",
        "mode",
      ],
      "op": "field",
      "type": {
        "type": "string",
      },
    },
    "op": "eq",
    "right": {
      "op": "literal",
      "type": "string",
      "value": "assistant",
    },
  },
  "from": {
    "name": "project_logs",
    "objects": [
      "495d041a-7790-4907-b3c7-20ee864569cc",
    ],
  },
  "select": [
    {
      "alias": "id",
      "expr": {
        "name": [
          "id",
        ],
        "op": "field",
        "type": {
          "description": "A unique identifier for the project logs event. If you don't provide one, Braintrust will generate one for you",
          "type": "string",
        },
      },
    },
    {
      "alias": "_xact_id",
      "expr": {
        "name": [
          "_xact_id",
        ],
        "op": "field",
        "type": {
          "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the project logs (see the \`version\` parameter)",
          "type": "string",
        },
      },
    },
    {
      "alias": "_pagination_key",
      "expr": {
        "name": [
          "_pagination_key",
        ],
        "op": "field",
        "type": {
          "description": "A stable, time-ordered key that can be used to paginate over project logs events. This field is auto-generated by Braintrust and only exists in Brainstore.",
          "type": [
            "string",
            "null",
          ],
        },
      },
    },
    {
      "alias": "created",
      "expr": {
        "name": [
          "created",
        ],
        "op": "field",
        "type": {
          "description": "The timestamp the project logs event was created",
          "format": "date-time",
          "type": "string",
        },
      },
    },
    {
      "alias": "org_id",
      "expr": {
        "name": [
          "org_id",
        ],
        "op": "field",
        "type": {
          "description": "Unique id for the organization that the project belongs under",
          "format": "uuid",
          "type": "string",
        },
      },
    },
    {
      "alias": "project_id",
      "expr": {
        "name": [
          "project_id",
        ],
        "op": "field",
        "type": {
          "description": "Unique identifier for the project",
          "format": "uuid",
          "type": "string",
        },
      },
    },
    {
      "alias": "log_id",
      "expr": {
        "name": [
          "log_id",
        ],
        "op": "field",
        "type": {
          "const": "g",
          "description": "A literal 'g' which identifies the log as a project log",
          "type": "string",
        },
      },
    },
    {
      "alias": "input",
      "expr": {
        "name": [
          "input",
        ],
        "op": "field",
        "type": {
          "description": "The arguments that uniquely define a user input (an arbitrary, JSON serializable object).",
        },
      },
    },
    {
      "alias": "output",
      "expr": {
        "name": [
          "output",
        ],
        "op": "field",
        "type": {
          "description": "The output of your application, including post-processing (an arbitrary, JSON serializable object), that allows you to determine whether the result is correct or not. For example, in an app that generates SQL queries, the \`output\` should be the _result_ of the SQL query generated by the model, not the query itself, because there may be multiple valid queries that answer a single question.",
        },
      },
    },
    {
      "alias": "expected",
      "expr": {
        "name": [
          "expected",
        ],
        "op": "field",
        "type": {
          "description": "The ground truth value (an arbitrary, JSON serializable object) that you'd compare to \`output\` to determine if your \`output\` value is correct or not. Braintrust currently does not compare \`output\` to \`expected\` for you, since there are so many different ways to do that correctly. Instead, these values are just used to help you navigate while digging into analyses. However, we may later use these values to re-score outputs or fine-tune your models.",
        },
      },
    },
    {
      "alias": "error",
      "expr": {
        "name": [
          "error",
        ],
        "op": "field",
        "type": {
          "description": "The error that occurred, if any.",
        },
      },
    },
    {
      "alias": "scores",
      "expr": {
        "name": [
          "scores",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "additionalProperties": {
                "anyOf": [
                  {
                    "maximum": 1,
                    "minimum": 0,
                    "type": "number",
                  },
                  {
                    "type": "null",
                  },
                ],
              },
              "properties": {},
              "type": "object",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
    {
      "alias": "metadata",
      "expr": {
        "name": [
          "metadata",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "additionalProperties": {},
              "properties": {
                "model": {
                  "description": "The model used for this example",
                  "type": [
                    "string",
                    "null",
                  ],
                },
              },
              "type": "object",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
    {
      "alias": "tags",
      "expr": {
        "name": [
          "tags",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "items": {
                "type": "string",
              },
              "type": "array",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
    {
      "alias": "metrics",
      "expr": {
        "name": [
          "metrics",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "additionalProperties": {
                "type": "number",
              },
              "properties": {
                "caller_filename": {
                  "description": "This metric is deprecated",
                },
                "caller_functionname": {
                  "description": "This metric is deprecated",
                },
                "caller_lineno": {
                  "description": "This metric is deprecated",
                },
                "completion_tokens": {
                  "anyOf": [
                    {
                      "type": "integer",
                    },
                    {
                      "type": "null",
                    },
                  ],
                },
                "end": {
                  "description": "A unix timestamp recording when the section of code which produced the project logs event finished",
                  "type": [
                    "number",
                    "null",
                  ],
                },
                "prompt_tokens": {
                  "anyOf": [
                    {
                      "type": "integer",
                    },
                    {
                      "type": "null",
                    },
                  ],
                },
                "start": {
                  "description": "A unix timestamp recording when the section of code which produced the project logs event started",
                  "type": [
                    "number",
                    "null",
                  ],
                },
                "tokens": {
                  "anyOf": [
                    {
                      "type": "integer",
                    },
                    {
                      "type": "null",
                    },
                  ],
                },
              },
              "type": "object",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
    {
      "alias": "context",
      "expr": {
        "name": [
          "context",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "additionalProperties": {},
              "properties": {
                "caller_filename": {
                  "description": "Name of the file in code where the project logs event was created",
                  "type": [
                    "string",
                    "null",
                  ],
                },
                "caller_functionname": {
                  "description": "The function in code which created the project logs event",
                  "type": [
                    "string",
                    "null",
                  ],
                },
                "caller_lineno": {
                  "anyOf": [
                    {
                      "type": "integer",
                    },
                    {
                      "type": "null",
                    },
                  ],
                },
              },
              "type": "object",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
    {
      "alias": "span_id",
      "expr": {
        "name": [
          "span_id",
        ],
        "op": "field",
        "type": {
          "description": "A unique identifier used to link different project logs events together as part of a full trace. See the [tracing guide](https://www.braintrust.dev/docs/guides/tracing) for full details on tracing",
          "type": "string",
        },
      },
    },
    {
      "alias": "span_parents",
      "expr": {
        "name": [
          "span_parents",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "items": {
                "type": "string",
              },
              "type": "array",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
    {
      "alias": "root_span_id",
      "expr": {
        "name": [
          "root_span_id",
        ],
        "op": "field",
        "type": {
          "description": "A unique identifier for the trace this project logs event belongs to",
          "type": "string",
        },
      },
    },
    {
      "alias": "is_root",
      "expr": {
        "name": [
          "is_root",
        ],
        "op": "field",
        "type": {
          "description": "Whether this span is a root span",
          "type": [
            "boolean",
            "null",
          ],
        },
      },
    },
    {
      "alias": "span_attributes",
      "expr": {
        "name": [
          "span_attributes",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "additionalProperties": {},
              "description": "Human-identifying attributes of the span, such as name, type, etc.",
              "properties": {
                "name": {
                  "description": "Name of the span, for display purposes only",
                  "type": [
                    "string",
                    "null",
                  ],
                },
                "type": {
                  "anyOf": [
                    {
                      "enum": [
                        "llm",
                        "score",
                        "function",
                        "eval",
                        "task",
                        "tool",
                      ],
                      "type": "string",
                    },
                    {
                      "type": "null",
                    },
                  ],
                },
              },
              "type": "object",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
    {
      "alias": "origin",
      "expr": {
        "name": [
          "origin",
        ],
        "op": "field",
        "type": {
          "anyOf": [
            {
              "description": "Reference to the original object and event this was copied from.",
              "properties": {
                "_xact_id": {
                  "description": "Transaction ID of the original event.",
                  "type": [
                    "string",
                    "null",
                  ],
                },
                "created": {
                  "description": "Created timestamp of the original event. Used to help sort in the UI",
                  "type": [
                    "string",
                    "null",
                  ],
                },
                "id": {
                  "description": "ID of the original event.",
                  "type": "string",
                },
                "object_id": {
                  "description": "ID of the object the event is originating from.",
                  "format": "uuid",
                  "type": "string",
                },
                "object_type": {
                  "description": "Type of the object the event is originating from.",
                  "enum": [
                    "experiment",
                    "dataset",
                    "prompt",
                    "function",
                    "prompt_session",
                    "project_logs",
                  ],
                  "type": "string",
                },
              },
              "required": [
                "object_type",
                "object_id",
                "id",
              ],
              "type": "object",
            },
            {
              "type": "null",
            },
          ],
        },
      },
    },
  ],
  "unpivot": [],
}
`;

exports[`binder corner cases 6`] = `
{
  "items": {
    "properties": {
      "_pagination_key": {
        "description": "A stable, time-ordered key that can be used to paginate over project logs events. This field is auto-generated by Braintrust and only exists in Brainstore.",
        "type": [
          "string",
          "null",
        ],
      },
      "_xact_id": {
        "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the project logs (see the \`version\` parameter)",
        "type": "string",
      },
      "context": {
        "anyOf": [
          {
            "additionalProperties": {},
            "properties": {
              "caller_filename": {
                "description": "Name of the file in code where the project logs event was created",
                "type": [
                  "string",
                  "null",
                ],
              },
              "caller_functionname": {
                "description": "The function in code which created the project logs event",
                "type": [
                  "string",
                  "null",
                ],
              },
              "caller_lineno": {
                "anyOf": [
                  {
                    "type": "integer",
                  },
                  {
                    "type": "null",
                  },
                ],
              },
            },
            "type": "object",
          },
          {
            "type": "null",
          },
        ],
      },
      "created": {
        "description": "The timestamp the project logs event was created",
        "format": "date-time",
        "type": "string",
      },
      "error": {
        "description": "The error that occurred, if any.",
      },
      "expected": {
        "description": "The ground truth value (an arbitrary, JSON serializable object) that you'd compare to \`output\` to determine if your \`output\` value is correct or not. Braintrust currently does not compare \`output\` to \`expected\` for you, since there are so many different ways to do that correctly. Instead, these values are just used to help you navigate while digging into analyses. However, we may later use these values to re-score outputs or fine-tune your models.",
      },
      "id": {
        "description": "A unique identifier for the project logs event. If you don't provide one, Braintrust will generate one for you",
        "type": "string",
      },
      "input": {
        "description": "The arguments that uniquely define a user input (an arbitrary, JSON serializable object).",
      },
      "is_root": {
        "description": "Whether this span is a root span",
        "type": [
          "boolean",
          "null",
        ],
      },
      "log_id": {
        "const": "g",
        "description": "A literal 'g' which identifies the log as a project log",
        "type": "string",
      },
      "metadata": {
        "anyOf": [
          {
            "additionalProperties": {},
            "properties": {
              "model": {
                "description": "The model used for this example",
                "type": [
                  "string",
                  "null",
                ],
              },
            },
            "type": "object",
          },
          {
            "type": "null",
          },
        ],
      },
      "metrics": {
        "anyOf": [
          {
            "additionalProperties": {
              "type": "number",
            },
            "properties": {
              "caller_filename": {
                "description": "This metric is deprecated",
              },
              "caller_functionname": {
                "description": "This metric is deprecated",
              },
              "caller_lineno": {
                "description": "This metric is deprecated",
              },
              "completion_tokens": {
                "anyOf": [
                  {
                    "type": "integer",
                  },
                  {
                    "type": "null",
                  },
                ],
              },
              "end": {
                "description": "A unix timestamp recording when the section of code which produced the project logs event finished",
                "type": [
                  "number",
                  "null",
                ],
              },
              "prompt_tokens": {
                "anyOf": [
                  {
                    "type": "integer",
                  },
                  {
                    "type": "null",
                  },
                ],
              },
              "start": {
                "description": "A unix timestamp recording when the section of code which produced the project logs event started",
                "type": [
                  "number",
                  "null",
                ],
              },
              "tokens": {
                "anyOf": [
                  {
                    "type": "integer",
                  },
                  {
                    "type": "null",
                  },
                ],
              },
            },
            "type": "object",
          },
          {
            "type": "null",
          },
        ],
      },
      "org_id": {
        "description": "Unique id for the organization that the project belongs under",
        "format": "uuid",
        "type": "string",
      },
      "origin": {
        "anyOf": [
          {
            "description": "Reference to the original object and event this was copied from.",
            "properties": {
              "_xact_id": {
                "description": "Transaction ID of the original event.",
                "type": [
                  "string",
                  "null",
                ],
              },
              "created": {
                "description": "Created timestamp of the original event. Used to help sort in the UI",
                "type": [
                  "string",
                  "null",
                ],
              },
              "id": {
                "description": "ID of the original event.",
                "type": "string",
              },
              "object_id": {
                "description": "ID of the object the event is originating from.",
                "format": "uuid",
                "type": "string",
              },
              "object_type": {
                "description": "Type of the object the event is originating from.",
                "enum": [
                  "experiment",
                  "dataset",
                  "prompt",
                  "function",
                  "prompt_session",
                  "project_logs",
                ],
                "type": "string",
              },
            },
            "required": [
              "object_type",
              "object_id",
              "id",
            ],
            "type": "object",
          },
          {
            "type": "null",
          },
        ],
      },
      "output": {
        "description": "The output of your application, including post-processing (an arbitrary, JSON serializable object), that allows you to determine whether the result is correct or not. For example, in an app that generates SQL queries, the \`output\` should be the _result_ of the SQL query generated by the model, not the query itself, because there may be multiple valid queries that answer a single question.",
      },
      "project_id": {
        "description": "Unique identifier for the project",
        "format": "uuid",
        "type": "string",
      },
      "root_span_id": {
        "description": "A unique identifier for the trace this project logs event belongs to",
        "type": "string",
      },
      "scores": {
        "anyOf": [
          {
            "additionalProperties": {
              "anyOf": [
                {
                  "maximum": 1,
                  "minimum": 0,
                  "type": "number",
                },
                {
                  "type": "null",
                },
              ],
            },
            "properties": {},
            "type": "object",
          },
          {
            "type": "null",
          },
        ],
      },
      "span_attributes": {
        "anyOf": [
          {
            "additionalProperties": {},
            "description": "Human-identifying attributes of the span, such as name, type, etc.",
            "properties": {
              "name": {
                "description": "Name of the span, for display purposes only",
                "type": [
                  "string",
                  "null",
                ],
              },
              "type": {
                "anyOf": [
                  {
                    "enum": [
                      "llm",
                      "score",
                      "function",
                      "eval",
                      "task",
                      "tool",
                    ],
                    "type": "string",
                  },
                  {
                    "type": "null",
                  },
                ],
              },
            },
            "type": "object",
          },
          {
            "type": "null",
          },
        ],
      },
      "span_id": {
        "description": "A unique identifier used to link different project logs events together as part of a full trace. See the [tracing guide](https://www.braintrust.dev/docs/guides/tracing) for full details on tracing",
        "type": "string",
      },
      "span_parents": {
        "anyOf": [
          {
            "items": {
              "type": "string",
            },
            "type": "array",
          },
          {
            "type": "null",
          },
        ],
      },
      "tags": {
        "anyOf": [
          {
            "items": {
              "type": "string",
            },
            "type": "array",
          },
          {
            "type": "null",
          },
        ],
      },
    },
    "type": "object",
  },
  "type": "array",
}
`;

exports[`binder corner cases 7`] = `
{
  "dimensions": [
    {
      "alias": "project_id",
      "expr": {
        "name": [
          "project_id",
        ],
        "op": "field",
        "type": {
          "description": "Unique identifier for the project that the experiment belongs under",
          "format": "uuid",
          "type": "string",
        },
      },
    },
  ],
  "filter": {
    "left": {
      "name": [
        "created",
      ],
      "op": "field",
      "type": {
        "description": "The timestamp the experiment event was created",
        "format": "date-time",
        "type": "string",
      },
    },
    "op": "ge",
    "right": {
      "op": "literal",
      "type": "datetime",
      "value": "2024-04-14T07:00:00Z",
    },
  },
  "from": {
    "name": "project",
    "objects": [],
  },
  "measures": [
    {
      "alias": "num_logs",
      "expr": {
        "args": [
          {
            "children": [
              {
                "name": [
                  "is_root",
                ],
                "op": "field",
                "type": {
                  "description": "Whether this span is a root span",
                  "type": [
                    "boolean",
                    "null",
                  ],
                },
              },
              {
                "left": {
                  "name": [
                    "log_id",
                  ],
                  "op": "field",
                  "type": {
                    "const": "g",
                    "description": "A literal 'g' which identifies the log as a project log",
                    "type": "string",
                  },
                },
                "op": "eq",
                "right": {
                  "op": "literal",
                  "type": "string",
                  "value": "g",
                },
              },
            ],
            "op": "and",
          },
        ],
        "name": "sum",
        "op": "function",
      },
    },
    {
      "alias": "tokens",
      "expr": {
        "args": [
          {
            "name": [
              "metrics",
              "tokens",
            ],
            "op": "field",
            "type": {
              "anyOf": [
                {
                  "type": "integer",
                },
                {
                  "type": "null",
                },
              ],
            },
          },
        ],
        "name": "sum",
        "op": "function",
      },
    },
    {
      "alias": "ttf",
      "expr": {
        "args": [
          {
            "name": [
              "metrics",
              "time_to_first_token",
            ],
            "op": "field",
            "type": {
              "type": "number",
            },
          },
        ],
        "name": "avg",
        "op": "function",
      },
    },
    {
      "alias": "p50_duration",
      "expr": {
        "args": [
          {
            "conds": [
              {
                "cond": {
                  "name": [
                    "is_root",
                  ],
                  "op": "field",
                  "type": {
                    "description": "Whether this span is a root span",
                    "type": [
                      "boolean",
                      "null",
                    ],
                  },
                },
                "then": {
                  "left": {
                    "name": [
                      "metrics",
                      "end",
                    ],
                    "op": "field",
                    "type": {
                      "description": "A unix timestamp recording when the section of code which produced the experiment event finished",
                      "type": [
                        "number",
                        "null",
                      ],
                    },
                  },
                  "op": "sub",
                  "right": {
                    "name": [
                      "metrics",
                      "start",
                    ],
                    "op": "field",
                    "type": {
                      "description": "A unix timestamp recording when the section of code which produced the experiment event started",
                      "type": [
                        "number",
                        "null",
                      ],
                    },
                  },
                },
              },
            ],
            "else": {
              "expr": {
                "op": "literal",
                "type": "null",
                "value": null,
              },
              "op": "cast",
              "type": "number",
            },
            "op": "if",
          },
          {
            "op": "literal",
            "type": "number",
            "value": 0.5,
          },
        ],
        "name": "percentile",
        "op": "function",
      },
    },
    {
      "alias": "p95_duration",
      "expr": {
        "args": [
          {
            "conds": [
              {
                "cond": {
                  "name": [
                    "is_root",
                  ],
                  "op": "field",
                  "type": {
                    "description": "Whether this span is a root span",
                    "type": [
                      "boolean",
                      "null",
                    ],
                  },
                },
                "then": {
                  "left": {
                    "name": [
                      "metrics",
                      "end",
                    ],
                    "op": "field",
                    "type": {
                      "description": "A unix timestamp recording when the section of code which produced the experiment event finished",
                      "type": [
                        "number",
                        "null",
                      ],
                    },
                  },
                  "op": "sub",
                  "right": {
                    "name": [
                      "metrics",
                      "start",
                    ],
                    "op": "field",
                    "type": {
                      "description": "A unix timestamp recording when the section of code which produced the experiment event started",
                      "type": [
                        "number",
                        "null",
                      ],
                    },
                  },
                },
              },
            ],
            "else": {
              "expr": {
                "op": "literal",
                "type": "null",
                "value": null,
              },
              "op": "cast",
              "type": "number",
            },
            "op": "if",
          },
          {
            "op": "literal",
            "type": "number",
            "value": 0.95,
          },
        ],
        "name": "percentile",
        "op": "function",
      },
    },
  ],
  "pivot": [],
  "unpivot": [],
}
`;

exports[`binder corner cases 8`] = `
{
  "items": {
    "properties": {
      "num_logs": {
        "type": [
          "integer",
          "null",
        ],
      },
      "p50_duration": {
        "type": [
          "number",
          "null",
        ],
      },
      "p95_duration": {
        "type": [
          "number",
          "null",
        ],
      },
      "project_id": {
        "description": "Unique identifier for the project that the experiment belongs under",
        "format": "uuid",
        "type": "string",
      },
      "tokens": {
        "type": [
          "integer",
          "null",
        ],
      },
      "ttf": {
        "type": [
          "number",
          "null",
        ],
      },
    },
    "type": "object",
  },
  "type": "array",
}
`;

exports[`binder corner cases 9`] = `
{
  "dimensions": [],
  "filter": null,
  "from": {
    "name": "project_logs",
    "objects": [
      "495d041a-7790-4907-b3c7-20ee864569cc",
    ],
  },
  "measures": [
    {
      "alias": "percentile(metrics.tokens, 0.95)",
      "expr": {
        "args": [
          {
            "name": [
              "metrics",
              "tokens",
            ],
            "op": "field",
            "type": {
              "anyOf": [
                {
                  "type": "integer",
                },
                {
                  "type": "null",
                },
              ],
            },
          },
          {
            "op": "literal",
            "type": "number",
            "value": 0.95,
          },
        ],
        "name": "percentile",
        "op": "function",
      },
    },
  ],
  "pivot": [],
  "unpivot": [],
}
`;

exports[`binder corner cases 10`] = `
{
  "items": {
    "properties": {
      "percentile(metrics.tokens, 0.95)": {
        "type": [
          "number",
          "null",
        ],
      },
    },
    "type": "object",
  },
  "type": "array",
}
`;

exports[`binder corner cases 11`] = `
{
  "filter": {
    "left": {
      "args": [
        {
          "name": [
            "span_attributes",
            "purpose",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
        {
          "op": "literal",
          "type": "string",
          "value": "default",
        },
      ],
      "name": "coalesce",
      "op": "function",
    },
    "op": "ne",
    "right": {
      "op": "literal",
      "type": "string",
      "value": "scorer",
    },
  },
  "from": {
    "name": "project_logs",
    "objects": [
      "495d041a-7790-4907-b3c7-20ee864569cc",
    ],
  },
  "select": [
    {
      "alias": "1",
      "expr": {
        "op": "literal",
        "type": "integer",
        "value": 1,
      },
    },
  ],
  "unpivot": [],
}
`;

exports[`binder corner cases 12`] = `
{
  "items": {
    "properties": {
      "1": {
        "type": "integer",
      },
    },
    "type": "object",
  },
  "type": "array",
}
`;
