#[cfg(test)]
mod tests {
    use crate::{postgres_pool::PostgresPool, test_init::test_init, test_util::PostgresContainer};
    use util::url::Url;

    #[tokio::test]
    async fn test_postgres_with_ssl_preferred() {
        test_init::init();

        // Test with a standard PostgreSQL container using sslmode=prefer
        // This verifies our rustls implementation can handle SSL negotiation
        let container = PostgresContainer::new().await;

        // Try connecting with sslmode=prefer (will use SSL if available)
        let ssl_url: Url = format!(
            "{}?sslmode=prefer",
            container.connection_url.as_str().trim_end_matches('/')
        )
        .parse()
        .unwrap();

        println!("Connecting to PostgreSQL with sslmode=prefer: {}", ssl_url);

        let pool = PostgresPool::new(&ssl_url, 5).unwrap();
        let client = pool.get_client().await.unwrap();

        // Check if the current connection is using SSL via pg_stat_ssl
        // This properly verifies whether THIS connection is using SSL, not just if SSL is enabled on the server
        let ssl_result = client
            .query_one(
                "SELECT ssl FROM pg_stat_ssl WHERE pid = pg_backend_pid()",
                &[],
            )
            .await;

        match ssl_result {
            Ok(row) => {
                let ssl_in_use: bool = row.get("ssl");
                println!("Current connection using SSL: {}", ssl_in_use);

                // Standard PostgreSQL container doesn't have SSL enabled,
                // so we expect SSL to NOT be in use
                assert!(
                    !ssl_in_use,
                    "Expected SSL to not be in use with standard container"
                );
            }
            Err(e) => {
                // pg_stat_ssl might not be available in some PostgreSQL versions
                println!("Could not query pg_stat_ssl: {}", e);
            }
        }

        // Test basic functionality
        let row = client
            .query_one("SELECT 'Connection works' as message", &[])
            .await
            .unwrap();

        let message: String = row.get("message");
        assert_eq!(message, "Connection works");

        println!("PostgreSQL connection with sslmode=prefer succeeded!");
    }

    #[tokio::test]
    async fn test_postgres_with_ssl_disabled() {
        test_init::init();

        // Test with sslmode=disable to verify SSL is NOT used
        let container = PostgresContainer::new().await;

        let no_ssl_url: Url = format!(
            "{}?sslmode=disable",
            container.connection_url.as_str().trim_end_matches('/')
        )
        .parse()
        .unwrap();

        println!(
            "Connecting to PostgreSQL with sslmode=disable: {}",
            no_ssl_url
        );

        let pool = PostgresPool::new(&no_ssl_url, 5).unwrap();
        let client = pool.get_client().await.unwrap();

        // Verify that SSL is NOT in use when explicitly disabled
        let ssl_result = client
            .query_one(
                "SELECT ssl FROM pg_stat_ssl WHERE pid = pg_backend_pid()",
                &[],
            )
            .await;

        match ssl_result {
            Ok(row) => {
                let ssl_in_use: bool = row.get("ssl");
                println!("Current connection using SSL: {}", ssl_in_use);

                // With sslmode=disable, SSL should definitely NOT be in use
                assert!(
                    !ssl_in_use,
                    "Expected SSL to be disabled when sslmode=disable"
                );
            }
            Err(e) => {
                println!("Could not query pg_stat_ssl: {}", e);
            }
        }

        // Test basic functionality
        let row = client
            .query_one("SELECT 'Non-SSL connection works' as message", &[])
            .await
            .unwrap();

        let message: String = row.get("message");
        assert_eq!(message, "Non-SSL connection works");

        println!("PostgreSQL connection with sslmode=disable succeeded!");
    }

    #[tokio::test]
    async fn test_postgres_pool_creation_with_ssl_urls() {
        test_init::init();

        // This test verifies that our PostgresPool with rustls can handle different SSL configurations
        let test_urls = vec![
            "postgres://user:pass@localhost:5432/db",
            "postgres://user:pass@localhost:5432/db?sslmode=disable",
            "postgres://user:pass@localhost:5432/db?sslmode=prefer",
            "postgres://user:pass@localhost:5432/db?sslmode=require",
        ];

        for url in test_urls {
            let url_parsed = url.parse().unwrap();
            // This should not panic with our rustls setup
            let pool = PostgresPool::new(&url_parsed, 1)
                .expect(&format!("Failed to create pool for {}", url));
            println!("Successfully created PostgreSQL pool with URL: {}", url);

            // Verify we can access the pool
            assert_eq!(pool.status().size, 0); // Pool starts empty
            assert_eq!(pool.status().max_size, 1);
        }
    }

    #[tokio::test]
    async fn test_redis_client_creation_with_tls_urls() {
        test_init::init();

        // Test that Redis client can be created with TLS URLs
        let tls_urls = vec![
            "rediss://localhost:6380",
            "redis://localhost:6379", // Non-TLS should also work
        ];

        for url in tls_urls {
            let _client = redis::Client::open(url)
                .expect(&format!("Failed to create Redis client for {}", url));
            println!("Successfully created Redis client with URL: {}", url);
        }
    }

    #[tokio::test]
    async fn test_postgres_invalid_cert_behavior() {
        test_init::init();

        // This test verifies our DangerousAcceptAnyCertificate implementation
        // by ensuring we can create pools that would accept invalid certificates

        // Create a URL that would require certificate validation
        let ssl_url = "postgres://user:<EMAIL>:5432/db?sslmode=require"
            .parse::<Url>()
            .unwrap();

        // This should succeed because our implementation accepts any certificate
        let pool = PostgresPool::new(&ssl_url, 1)
            .expect("Pool creation should succeed with DangerousAcceptAnyCertificate");

        println!("Successfully created pool with sslmode=require (accepts any cert)");

        // We can't actually connect without a real server, but pool creation proves
        // our rustls configuration is working
        assert_eq!(pool.status().max_size, 1);
    }
}
