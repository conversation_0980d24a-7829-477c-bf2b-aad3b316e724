measures: percentile(scores.foo, 0.0) as p0;
measures: percentile(scores.foo, 0.01) as p1;
measures: percentile(scores.foo, 0.05) as p5;
measures: percentile(scores.foo, 0.10) as p10;
measures: percentile(scores.foo, 0.25) as p25;
measures: percentile(scores.foo, 0.50) as p50;
measures: percentile(scores.foo, 0.75) as p75;
measures: percentile(scores.foo, 0.90) as p90;
measures: percentile(scores.foo, 0.95) as p95;
measures: percentile(scores.foo, 0.99) as p99;
measures: percentile(scores.foo, 1.0) as p100;

measures: percentile(scores.foo, 0.0) as p0 | filter: metadata.split = 'train';
measures: percentile(scores.foo, 0.05) as p5 | filter: metadata.split = 'train';
measures: percentile(scores.foo, 0.25) as p25 | filter: metadata.split = 'train';
measures: percentile(scores.foo, 0.75) as p75 | filter: metadata.split = 'train';
measures: percentile(scores.foo, 0.95) as p95 | filter: metadata.split = 'train';
measures: percentile(scores.foo, 1.0) as p100 | filter: metadata.split = 'train';

measures: percentile(scores.foo, 0.0) as p0 | filter: metadata.split = 'test';
measures: percentile(scores.foo, 0.10) as p10 | filter: metadata.split = 'test';
measures: percentile(scores.foo, 0.50) as p50 | filter: metadata.split = 'test';
measures: percentile(scores.foo, 0.90) as p90 | filter: metadata.split = 'test';
measures: percentile(scores.foo, 1.0) as p100 | filter: metadata.split = 'test';

measures: percentile(scores.foo, 0.0) as p0 | filter: null;
measures: percentile(scores.foo, 1.0) as p100 | filter: null;

measures: percentile(counts.foo, 0.0) as p0;
measures: percentile(counts.foo, 0.01) as p1;
measures: percentile(counts.foo, 0.05) as p5;
measures: percentile(counts.foo, 0.10) as p10;
measures: percentile(counts.foo, 0.25) as p25;
measures: percentile(counts.foo, 0.50) as p50;
measures: percentile(counts.foo, 0.75) as p75;
measures: percentile(counts.foo, 0.90) as p90;
measures: percentile(counts.foo, 0.95) as p95;
measures: percentile(counts.foo, 0.99) as p99;
measures: percentile(counts.foo, 1.0) as p100;

measures: percentile(counts.foo, 0.0) as p0 | filter: metadata.split = 'train';
measures: percentile(counts.foo, 0.05) as p5 | filter: metadata.split = 'train';
measures: percentile(counts.foo, 0.25) as p25 | filter: metadata.split = 'train';
measures: percentile(counts.foo, 0.75) as p75 | filter: metadata.split = 'train';
measures: percentile(counts.foo, 0.95) as p95 | filter: metadata.split = 'train';
measures: percentile(counts.foo, 1.0) as p100 | filter: metadata.split = 'train';

measures: percentile(counts.foo, 0.0) as p0 | filter: metadata.split = 'test';
measures: percentile(counts.foo, 0.10) as p10 | filter: metadata.split = 'test';
measures: percentile(counts.foo, 0.50) as p50 | filter: metadata.split = 'test';
measures: percentile(counts.foo, 0.90) as p90 | filter: metadata.split = 'test';
measures: percentile(counts.foo, 1.0) as p100 | filter: metadata.split = 'test';

measures: percentile(counts.foo, 0.50) as p50 | filter: null;
