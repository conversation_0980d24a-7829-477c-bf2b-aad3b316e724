[{"error": null, "query": "measures: percentile(scores.foo, 0.0) as p0", "result_rows": [{"p0": -1}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.01) as p1", "result_rows": [{"p1": -0.99}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.05) as p5", "result_rows": [{"p5": -0.895786}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.10) as p10", "result_rows": [{"p10": -0.763334}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.25) as p25", "result_rows": [{"p25": 0.044594}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.50) as p50", "result_rows": [{"p50": 0.229903}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.75) as p75", "result_rows": [{"p75": 0.704645}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.90) as p90", "result_rows": [{"p90": 0.95118}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.95) as p95", "result_rows": [{"p95": 0.970396}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.99) as p99", "result_rows": [{"p99": 0.99}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 1.0) as p100", "result_rows": [{"p100": 1}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.0) as p0 | filter: metadata.split = 'train'", "result_rows": [{"p0": -1}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.05) as p5 | filter: metadata.split = 'train'", "result_rows": [{"p5": -0.913883}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.25) as p25 | filter: metadata.split = 'train'", "result_rows": [{"p25": -0.522008}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.75) as p75 | filter: metadata.split = 'train'", "result_rows": [{"p75": 0.342979}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.95) as p95 | filter: metadata.split = 'train'", "result_rows": [{"p95": 0.624961}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 1.0) as p100 | filter: metadata.split = 'train'", "result_rows": [{"p100": 0.7025}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.0) as p0 | filter: metadata.split = 'test'", "result_rows": [{"p0": 0.7214}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.10) as p10 | filter: metadata.split = 'test'", "result_rows": [{"p10": 0.763334}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.50) as p50 | filter: metadata.split = 'test'", "result_rows": [{"p50": 0.932345}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.90) as p90 | filter: metadata.split = 'test'", "result_rows": [{"p90": 0.99}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 1.0) as p100 | filter: metadata.split = 'test'", "result_rows": [{"p100": 1}], "skip": false}, {"error": "Unsupported operation: non-boolean literal in filter", "query": "measures: percentile(scores.foo, 0.0) as p0 | filter: null", "result_rows": [], "skip": true}, {"error": "Unsupported operation: non-boolean literal in filter", "query": "measures: percentile(scores.foo, 1.0) as p100 | filter: null", "result_rows": [], "skip": true}, {"error": null, "query": "measures: percentile(counts.foo, 0.0) as p0", "result_rows": [{"p0": 0}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.01) as p1", "result_rows": [{"p1": 0}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.05) as p5", "result_rows": [{"p5": 0}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.10) as p10", "result_rows": [{"p10": 0}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.25) as p25", "result_rows": [{"p25": 1.993662}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.50) as p50", "result_rows": [{"p50": 43.383348}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.75) as p75", "result_rows": [{"p75": 46644.409149}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.90) as p90", "result_rows": [{"p90": 6140833.430401}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.95) as p95", "result_rows": [{"p95": 31658768.01597}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.99) as p99", "result_rows": [{"p99": 84356157.806771}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 1.0) as p100", "result_rows": [{"p100": 98700000}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.0) as p0 | filter: metadata.split = 'train'", "result_rows": [{"p0": 2}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.05) as p5 | filter: metadata.split = 'train'", "result_rows": [{"p5": 2.974233}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.25) as p25 | filter: metadata.split = 'train'", "result_rows": [{"p25": 17.994143}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.75) as p75 | filter: metadata.split = 'train'", "result_rows": [{"p75": 311879.348809}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.95) as p95 | filter: metadata.split = 'train'", "result_rows": [{"p95": 41889050.769913}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 1.0) as p100 | filter: metadata.split = 'train'", "result_rows": [{"p100": 98700000}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.0) as p0 | filter: metadata.split = 'test'", "result_rows": [{"p0": 0}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.10) as p10 | filter: metadata.split = 'test'", "result_rows": [{"p10": 0}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.50) as p50 | filter: metadata.split = 'test'", "result_rows": [{"p50": 0}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 0.90) as p90 | filter: metadata.split = 'test'", "result_rows": [{"p90": 0.99}], "skip": false}, {"error": null, "query": "measures: percentile(counts.foo, 1.0) as p100 | filter: metadata.split = 'test'", "result_rows": [{"p100": 2}], "skip": false}, {"error": "Unsupported operation: non-boolean literal in filter", "query": "measures: percentile(counts.foo, 0.50) as p50 | filter: null", "result_rows": [], "skip": true}]