[{"error": null, "query": "measures: count(1)", "result_rows": [{"count(1)": 5}], "skip": false}, {"error": null, "query": "-- This should still return a single row with 0\nmeasures: count(1) | filter: false", "result_rows": [{"count(1)": 0}], "skip": false}, {"error": null, "query": "measures: count(1)*2", "result_rows": [{"count(1)*2": 10}], "skip": false}, {"error": null, "query": "measures: count(null)", "result_rows": [{"count(null)": 0}], "skip": false}, {"error": null, "query": "measures: count(null) + count(1)", "result_rows": [{"count(null) + count(1)": 5}], "skip": false}, {"error": null, "query": "measures: count(scores.foo), count(scores.bar)", "result_rows": [{"count(scores.bar)": 1, "count(scores.foo)": 3}], "skip": false}, {"error": null, "query": "measures: sum(null)", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: sum(scores.foo)", "result_rows": [{"sum(scores.foo)": 1.25}], "skip": false}, {"error": null, "query": "measures: sum(scores.bar)", "result_rows": [{"sum(scores.bar)": 0.5}], "skip": false}, {"error": null, "query": "measures: sum(scores.foo)*2", "result_rows": [{"sum(scores.foo)*2": 2.5}], "skip": false}, {"error": null, "query": "measures: sum(scores.foo) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: sum(scores.foo) | filter: scores.bar = 0.5", "result_rows": [{"sum(scores.foo)": 0}], "skip": false}, {"error": null, "query": "measures: sum(scores.bar) | filter: scores.foo = 0", "result_rows": [{"sum(scores.bar)": 0.5}], "skip": false}, {"error": null, "query": "measures: sum(scores.bar) | filter: scores.foo = 1", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: sum(scores.bar) | filter: scores.foo = 1.0", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: sum(scores.foo) | filter: scores.foo = 7 - 6", "result_rows": [{"sum(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: sum(scores.foo) | filter: scores.foo = 0.5 + 0.5", "result_rows": [{"sum(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: sum(counts.foo)", "result_rows": [{"sum(counts.foo)": 6}], "skip": false}, {"error": null, "query": "measures: sum(counts.bar)", "result_rows": [{"sum(counts.bar)": 2}], "skip": false}, {"error": null, "query": "measures: sum(counts.bar)*3", "result_rows": [{"sum(counts.bar)*3": 6}], "skip": false}, {"error": null, "query": "measures: sum(counts.foo) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: sum(counts.foo) | filter: counts.bar = 2", "result_rows": [{"sum(counts.foo)": -7}], "skip": false}, {"error": null, "query": "measures: sum(counts.bar) | filter: counts.foo = 3", "result_rows": [{"sum(counts.bar)": 0}], "skip": false}, {"error": null, "query": "measures: sum(counts.bar) | filter: counts.foo = 10", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: sum(counts.bar) | filter: counts.foo = 10.0", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: sum(counts.foo) | filter: counts.foo = 5 * 2", "result_rows": [{"sum(counts.foo)": 10}], "skip": false}, {"error": null, "query": "measures: sum(counts.foo) | filter: counts.foo = 1.2 + 8.8", "result_rows": [{"sum(counts.foo)": 10}], "skip": false}, {"error": null, "query": "-- Sum > i64 max (forces u64)\nmeasures: sum(counts.huge)", "result_rows": [{"sum(counts.huge)": -9223372036854775807}], "skip": false}, {"error": null, "query": "measures: avg(null)", "result_rows": [{"avg(null)": 0}], "skip": false}, {"error": null, "query": "measures: avg(scores.foo)", "result_rows": [{"avg(scores.foo)": 0.25}], "skip": false}, {"error": null, "query": "measures: avg(scores.bar)", "result_rows": [{"avg(scores.bar)": 0.1}], "skip": false}, {"error": null, "query": "measures: avg(scores.foo)*2", "result_rows": [{"avg(scores.foo)*2": 0.5}], "skip": false}, {"error": null, "query": "measures: avg(scores.foo) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: avg(scores.foo) | filter: scores.bar = 0.5", "result_rows": [{"avg(scores.foo)": 0}], "skip": false}, {"error": null, "query": "measures: avg(scores.bar) | filter: scores.foo = 0", "result_rows": [{"avg(scores.bar)": 0.5}], "skip": false}, {"error": null, "query": "measures: avg(scores.bar) | filter: scores.foo = 1", "result_rows": [{"avg(scores.bar)": 0}], "skip": false}, {"error": null, "query": "measures: avg(scores.bar) | filter: scores.foo = 1.0", "result_rows": [{"avg(scores.bar)": 0}], "skip": false}, {"error": null, "query": "measures: avg(scores.foo) | filter: scores.foo = 7 - 6", "result_rows": [{"avg(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: avg(scores.foo) | filter: scores.foo = 0.5 + 0.5", "result_rows": [{"avg(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: avg(counts.foo)", "result_rows": [{"avg(counts.foo)": 1.2}], "skip": false}, {"error": null, "query": "measures: avg(counts.bar)", "result_rows": [{"avg(counts.bar)": 0.4}], "skip": false}, {"error": null, "query": "measures: avg(counts.bar)*3", "result_rows": [{"avg(counts.bar)*3": 1.2}], "skip": false}, {"error": null, "query": "measures: avg(counts.foo) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: avg(counts.foo) | filter: counts.bar = 2", "result_rows": [{"avg(counts.foo)": -7}], "skip": false}, {"error": null, "query": "measures: avg(counts.bar) | filter: counts.foo = 3", "result_rows": [{"avg(counts.bar)": 0}], "skip": false}, {"error": null, "query": "measures: avg(counts.bar) | filter: counts.foo = 10", "result_rows": [{"avg(counts.bar)": 0}], "skip": false}, {"error": null, "query": "measures: avg(counts.bar) | filter: counts.foo = 10.0", "result_rows": [{"avg(counts.bar)": 0}], "skip": false}, {"error": null, "query": "measures: avg(counts.foo) | filter: counts.foo = 5 * 2", "result_rows": [{"avg(counts.foo)": 10}], "skip": false}, {"error": null, "query": "measures: avg(counts.foo) | filter: counts.foo = 1.2 + 8.8", "result_rows": [{"avg(counts.foo)": 10}], "skip": false}, {"error": null, "query": "measures: avg(counts.huge)", "result_rows": [{"avg(counts.huge)": -1844674407370955264}], "skip": false}, {"error": null, "query": "measures: min(null)", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: min(scores.foo)", "result_rows": [{"min(scores.foo)": 0}], "skip": false}, {"error": null, "query": "measures: min(scores.bar)", "result_rows": [{"min(scores.bar)": 0.5}], "skip": false}, {"error": null, "query": "measures: min(scores.foo)*2", "result_rows": [{"min(scores.foo)*2": 0}], "skip": false}, {"error": null, "query": "measures: min(scores.foo) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: min(scores.foo) | filter: scores.bar = 0.5", "result_rows": [{"min(scores.foo)": 0}], "skip": false}, {"error": null, "query": "measures: min(scores.bar) | filter: scores.foo = 0", "result_rows": [{"min(scores.bar)": 0.5}], "skip": false}, {"error": null, "query": "measures: min(scores.bar) | filter: scores.foo = 1", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: min(scores.bar) | filter: scores.foo = 1.0", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: min(scores.foo) | filter: scores.foo = 7 - 6", "result_rows": [{"min(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: min(scores.foo) | filter: scores.foo = 0.5 + 0.5", "result_rows": [{"min(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: min(counts.foo)", "result_rows": [{"min(counts.foo)": -7}], "skip": false}, {"error": null, "query": "measures: min(counts.bar)", "result_rows": [{"min(counts.bar)": 0}], "skip": false}, {"error": null, "query": "measures: min(counts.bar)*3", "result_rows": [{"min(counts.bar)*3": 0}], "skip": false}, {"error": null, "query": "measures: min(counts.foo) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: min(counts.foo) | filter: counts.bar = 2", "result_rows": [{"min(counts.foo)": -7}], "skip": false}, {"error": null, "query": "measures: min(counts.bar) | filter: counts.foo = 3", "result_rows": [{"min(counts.bar)": 0}], "skip": false}, {"error": null, "query": "measures: min(counts.bar) | filter: counts.foo = 10", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: min(counts.bar) | filter: counts.foo = 10.0", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: min(counts.foo) | filter: counts.foo = 5 * 2", "result_rows": [{"min(counts.foo)": 10}], "skip": false}, {"error": null, "query": "measures: min(counts.foo) | filter: counts.foo = 1.2 + 8.8", "result_rows": [{"min(counts.foo)": 10}], "skip": false}, {"error": null, "query": "measures: min(counts.huge)", "result_rows": [{"min(counts.huge)": -3}], "skip": false}, {"error": null, "query": "measures: max(null)", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(scores.foo)", "result_rows": [{"max(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: max(scores.bar)", "result_rows": [{"max(scores.bar)": 0.5}], "skip": false}, {"error": null, "query": "measures: max(scores.foo)*2", "result_rows": [{"max(scores.foo)*2": 2}], "skip": false}, {"error": null, "query": "measures: max(scores.foo) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(scores.foo) | filter: scores.bar = 0.5", "result_rows": [{"max(scores.foo)": 0}], "skip": false}, {"error": null, "query": "measures: max(scores.bar) | filter: scores.foo = 0", "result_rows": [{"max(scores.bar)": 0.5}], "skip": false}, {"error": null, "query": "measures: max(scores.bar) | filter: scores.foo = 1", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(scores.bar) | filter: scores.foo = 1.0", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(scores.foo) | filter: scores.foo = 7 - 6", "result_rows": [{"max(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: max(scores.foo) | filter: scores.foo = 0.5 + 0.5", "result_rows": [{"max(scores.foo)": 1}], "skip": false}, {"error": null, "query": "measures: max(counts.foo)", "result_rows": [{"max(counts.foo)": 10}], "skip": false}, {"error": null, "query": "measures: max(counts.bar)", "result_rows": [{"max(counts.bar)": 2}], "skip": false}, {"error": null, "query": "measures: max(counts.bar)*3", "result_rows": [{"max(counts.bar)*3": 6}], "skip": false}, {"error": null, "query": "measures: max(counts.foo) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(counts.foo) | filter: counts.bar = 2", "result_rows": [{"max(counts.foo)": -7}], "skip": false}, {"error": null, "query": "measures: max(counts.bar) | filter: counts.foo = 3", "result_rows": [{"max(counts.bar)": 0}], "skip": false}, {"error": null, "query": "measures: max(counts.bar) | filter: counts.foo = 10", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(counts.bar) | filter: counts.foo = 10", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(counts.bar) | filter: counts.foo = 10.0", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(counts.foo) | filter: counts.foo = 5 * 2", "result_rows": [{"max(counts.foo)": 10}], "skip": false}, {"error": null, "query": "measures: max(counts.foo) | filter: counts.foo = 1.2 + 8.8", "result_rows": [{"max(counts.foo)": 10}], "skip": false}, {"error": null, "query": "measures: max(counts.huge)", "result_rows": [{"max(counts.huge)": 9223372036854775807}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.5) as p50", "result_rows": [{"p50": 0.249051}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.9) as p90", "result_rows": [{"p90": 0.249051}], "skip": false}, {"error": null, "query": "measures: percentile(scores.foo, 0.0) as p0", "result_rows": [{"p0": 0}], "skip": false}]