use ::query::interpreter::context::default_query_timeout_seconds;
use actix_web::{
    get, post,
    web::{self, Bytes},
    App, HttpRequest, HttpResponse, HttpServer, Responder,
};
use agent::tracing_opentelemetry::OpenTelemetrySpanExt;
use otel_common::{
    opentelemetry,
    opentelemetry::metrics::{Counter, Histogram},
    opentelemetry::trace::{TraceContextExt, TraceId},
};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::{collections::BTreeMap, future::Future, sync::Arc};
use storage::{
    backfill::{backfill_worker, BackfillWorkerInput, BackfillWorkerOptions},
    compaction_loop::CompactionLoop,
    global_locks_manager::GlobalLocksManager,
    global_store::SegmentWalEntriesXactIdStatistic,
    index_wal_reader::IndexWalReaderOpts,
    merge::MergeOpts,
    optimize_tantivy_index::{
        OptimizationLoopType, OptimizeAllObjectsOptions, OptimizeObjectOptions,
        OptimizeObjectTuningOptions,
    },
    process_wal::PAGINATION_KEY_FIELD,
    worker_state::{get_vacuum_status, reset_vacuum_state},
};
use tokio::sync::{mpsc, Mutex};
use tokio_stream::{wrappers::ReceiverStream, StreamExt};

use clap::Parser;

use tracing::instrument::Instrument;
use util::{
    anyhow::Result, futures::join, global_opts::suppress_verbose_info, once_cell::sync::Lazy,
    ptree::MakePTree, system_types::FullObjectIdOwned, xact::PaginationKey,
};

use crate::{
    base::{
        AppState, BaseArgs, CLIArgs, ProcessGuard, TimeBasedRetentionWorkerOptions, VacuumOptions,
        WebOpts,
    },
    btql::{self, default_num_query_threads},
    cleanup::CleanupCommand,
    command_proc::{CommandProc, DEFAULT_LIFETIME_EXECUTIONS},
    compaction_worker::run_compaction_loop,
    executor_pool::make_executor_pool,
    index,
    pg_pool::get_pg_conn,
    retention, vacuum, wal,
};

struct WebMetrics {
    query_latency: Histogram<u64>,
    query_rows_returned: Counter<u64>,
    query_errors: Counter<u64>,
    endpoint_requests: Counter<u64>,
}

impl WebMetrics {
    pub fn new() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            query_latency: meter
                .u64_histogram("brainstore.web.query.latency_ms")
                .with_description("Query execution latency in milliseconds")
                .build(),
            query_rows_returned: meter
                .u64_counter("brainstore.web.query.rows_returned")
                .with_description("Total number of rows returned by queries")
                .build(),
            query_errors: meter
                .u64_counter("brainstore.web.query.errors")
                .with_description("Total number of query errors")
                .build(),
            endpoint_requests: meter
                .u64_counter("brainstore.web.endpoint.requests")
                .with_description("Total number of requests per endpoint")
                .build(),
        }
    }
}

static WEB_METRICS: Lazy<WebMetrics> = Lazy::new(|| WebMetrics::new());

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct WebCommand {
    #[command(flatten)]
    pub base: BaseArgs,

    #[arg(
        long,
        help = "The host to listen on",
        default_value = "0.0.0.0",
        env = "BRAINSTORE_WEB_HOST"
    )]
    pub host: String,

    #[arg(
        long,
        help = "The port to listen on",
        default_value = "4000",
        env = "BRAINSTORE_WEB_PORT"
    )]
    pub port: u16,

    #[arg(
        long,
        default_value_t = default_num_query_threads(),
        help = "The number of threads to use for query execution (defaults to 64)",
        env = "BRAINSTORE_WEB_QUERY_THREADS"
    )]
    pub num_query_threads: usize,

    #[arg(
        long,
        help = "The logical schema to use for binding BTQL queries.",
        env = "BRAINSTORE_WEB_SCHEMA"
    )]
    pub schema: Option<String>,

    #[arg(
        long,
        help = "The lifetime execution count for a command process worker.",
        default_value_t = default_lifetime_executions(),
        env = "BRAINSTORE_WEB_COMMAND_PROC_LIFETIME"
    )]
    pub command_proc_lifetime_executions: usize,

    #[command(flatten)]
    pub process_wal_opts: storage::process_wal::ProcessObjectWalOptions,

    #[command(flatten)]
    pub wal_compact_opts: storage::process_wal::CompactSegmentWalOptions,

    #[command(flatten)]
    pub index_wal_reader_opts: IndexWalReaderOpts,

    #[arg(
        long,
        env = "BRAINSTORE_STREAMING_QUERY_BUFFER_SIZE",
        value_parser = util::ByteSize::parse_to_usize,
        default_value_t = default_streaming_query_buffer_size(),
        help = format!("The total memory budget for the streaming query buffer (defaults to {})", util::ByteSize::from(default_streaming_query_buffer_size())),
    )]
    #[serde(default = "default_streaming_query_buffer_size")]
    pub streaming_query_buffer_size: usize,

    #[arg(
        long,
        default_value_t = false,
        env = "BRAINSTORE_DISABLE_OPTIMIZATION_WORKER",
        help = "If true, disables the optimization worker."
    )]
    pub disable_optimization_worker: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub optimize_all_objects_input: crate::index::OptimizeAllObjectsInput,

    #[command(flatten)]
    #[serde(flatten)]
    pub optimize_object_tuning_opts: OptimizeObjectTuningOptions,

    #[arg(
        long,
        default_value_t = false,
        env = "BRAINSTORE_DISABLE_VACUUM_WORKER",
        help = "If true, disables the vacuum worker."
    )]
    pub disable_vacuum_worker: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub vacuum_options: VacuumOptions,

    #[arg(
        long,
        default_value_t = false,
        env = "BRAINSTORE_DISABLE_TIME_BASED_RETENTION_WORKER",
        help = "If true, disables the time-based retention worker."
    )]
    pub disable_time_based_retention_worker: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub time_based_retention_worker_options: TimeBasedRetentionWorkerOptions,

    #[arg(
        long,
        default_value_t = default_query_timeout_seconds(),
        env = "BRAINSTORE_QUERY_TIMEOUT_SECONDS",
        help = format!("The timeout for a query"),
    )]
    #[serde(default = "default_query_timeout_seconds")]
    pub query_timeout_seconds: usize,

    #[arg(
        long,
        default_value_t = default_query_log_threshold_seconds(),
        env = "BRAINSTORE_QUERY_LOG_THRESHOLD_SECONDS",
        help = format!("The threshold after which the query plan is logged"),
    )]
    #[serde(default = "default_query_log_threshold_seconds")]
    pub slow_query_log_threshold_seconds: usize,

    #[arg(
        long,
        default_value_t = false,
        env = "BRAINSTORE_DISABLE_PROCESS_WAL_WORKER",
        help = "If true, disables the wal-processing worker."
    )]
    pub disable_process_wal_worker: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub backfill_worker_options: BackfillWorkerOptions,
}

impl WebCommand {
    pub fn brainstore_role(&self) -> &'static str {
        if self.disable_process_wal_worker && self.disable_optimization_worker {
            "reader"
        } else {
            "writer"
        }
    }

    pub fn additional_otel_attributes(&self) -> Vec<otel_common::opentelemetry::KeyValue> {
        vec![otel_common::opentelemetry::KeyValue::new(
            "brainstore_role",
            self.brainstore_role(),
        )]
    }
}

fn default_lifetime_executions() -> usize {
    DEFAULT_LIFETIME_EXECUTIONS
}

fn default_streaming_query_buffer_size() -> usize {
    100 * 1024 * 1024 // 100mb
}

fn default_query_log_threshold_seconds() -> usize {
    30
}

struct WebState {
    state: Arc<AppState>,
    opts: WebOpts,
    compaction_loop: CompactionLoop,
}

pub async fn main(args: WebCommand, command_proc: CommandProc) -> Result<util::Value> {
    let mut state = AppState::new(&args.base)?;
    {
        let state = Arc::get_mut(&mut state).unwrap();
        state.set_default_logical_schema(args.schema.clone());

        state.set_command_proc(Some(command_proc));

        // We want each query execution request to have its own thread pool, so that they don't fight and
        // starve each other from resources. Instead of spinning up a bunch of new threads each time, though,
        // we'll use a pool so that the threads are reused.
        //
        // If we find that this is overloading the server, we can limit the size of this pool, so that only N
        // concurrent queries are running at once.
        state.set_executor_pool(
            make_executor_pool(args.num_query_threads, "web")
                .build()
                .unwrap(),
        );
    }

    // Store additional_otel_attributes before moving fields from args
    let additional_otel_attributes = args.additional_otel_attributes();

    let web_opts = WebOpts {
        process_wal_options: args.process_wal_opts,
        compact_wal_options: args.wal_compact_opts.clone(),
        optimize_all_objects_input: index::OptimizeAllObjectsInput {
            loop_type: OptimizationLoopType::LongTerm,
            iterations: Some(
                args.optimize_all_objects_input
                    .iterations
                    .unwrap_or(10)
                    // Since we sleep for 10 seconds each time, this means we'll go through each object once every half hour
                    .max(180),
            ),
            ignore_old_segment_object_ids: args
                .optimize_all_objects_input
                .ignore_old_segment_object_ids,
        },
        optimize_object_tuning_opts: args.optimize_object_tuning_opts,
        vacuum_options: args.vacuum_options,
        time_based_retention_worker_options: args.time_based_retention_worker_options,
        index_wal_reader_opts: args.index_wal_reader_opts,
        streaming_query_buffer_size: args.streaming_query_buffer_size,
        query_timeout_seconds: args.query_timeout_seconds,
        slow_query_log_threshold_seconds: args.slow_query_log_threshold_seconds,
        backfill_worker_options: args.backfill_worker_options,
    };

    let compaction_loop = CompactionLoop::default();

    {
        let compaction_loop_clone = compaction_loop.clone();
        let state_clone = state.clone();
        let wal_compact_opts = args.wal_compact_opts.clone();
        tokio::spawn(launch_background_worker("compaction_loop", move || {
            let compaction_loop = compaction_loop_clone.clone();
            let state = state_clone.clone();
            let opts = wal_compact_opts.clone();
            async move { run_compaction_loop(compaction_loop, state, opts).await }
        }));
    }

    let web_state = Arc::new(WebState {
        state: state.clone(),
        compaction_loop: compaction_loop.clone(),
        opts: web_opts,
    });

    if !args.disable_optimization_worker {
        let web_state_clone = web_state.clone();
        tokio::spawn(launch_background_worker(
            "long_term_optimization_worker",
            move || {
                let web_state = web_state_clone.clone();
                async move { web_optimize_loop(web_state, OptimizationLoopType::LongTerm).await }
            },
        ));

        let web_state_clone = web_state.clone();
        tokio::spawn(launch_background_worker(
            "short_compact_optimization_worker",
            move || {
                let web_state = web_state_clone.clone();
                async move { web_optimize_loop(web_state, OptimizationLoopType::ShortCompact).await }
            },
        ));
    }

    crate::cleanup::check_can_run_cleanup().await?;
    crate::cleanup::launch_cleanup_thread(
        args.base.clone(),
        CleanupCommand {
            delete: true,
            idle_seconds: crate::cleanup::default_idle_seconds(),
        },
    );

    if args.base.telemetry_args.metrics_enabled() {
        tokio::spawn(launch_background_worker("metrics_loop", move || {
            crate::metrics::run_metrics_loop(
                state.clone(),
                compaction_loop.clone(),
                args.base.telemetry_args.metrics_interval_seconds,
            )
        }));
    }

    match (
        args.base.telemetry_args.control_plane_status_enabled(),
        &args.base.license_key,
    ) {
        (true, Some(license_key)) => {
            launch_status_reporting_loop(
                web_state.clone(),
                args.base.app_url.clone(),
                license_key.clone(),
                additional_otel_attributes,
            );
        }
        _ => {}
    }

    if !args.disable_vacuum_worker {
        launch_vacuum_worker(web_state.clone());
    }

    if !args.disable_process_wal_worker {
        launch_process_wal_worker(web_state.clone());
    }

    match crate::GIT_COMMIT {
        commit => {
            log::info!("Starting web server with commit: {}", commit);
        }
    };

    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(web_state.clone()))
            // 1gb payload limit
            .app_data(web::JsonConfig::default().limit(1024 * 1024 * 1024))
            .service(ping)
            .service(version)
            .service(wal_insert)
            .service(wal_process)
            .service(wal_compact)
            .service(wal_cat)
            .service(wal_status)
            .service(index_merge)
            .service(index_delete)
            .service(index_optimize)
            .service(vacuum_status)
            .service(vacuum_reset_state)
            // Temporarily disable the retention and vacuum endpoints until
            // we're ready to use them.
            // .service(vacuum_segment_wal)
            // .service(time_based_retention)
            .service(vacuum_index_stateless)
            .service(vacuum_index)
            .service(query)
            .service(processes)
            .service(object_info)
            .service(dump_memprof)
            .service(status)
            .service(locks_snapshot)
            .service(locks_sanity_check)
    })
    .bind((args.host, args.port))?
    .run()
    .await?;

    Ok(util::Value::Null)
}

async fn wrap_response<T: Serialize>(
    span: tracing::Span,
    cmd: &str,
    args: serde_json::Value,
    state: Arc<WebState>,
    result: impl Future<Output = Result<T>>,
) -> impl Responder {
    let _pid = state.state.process_list.start(cmd, args);
    let trace_id = get_trace_id(&span);

    // Record endpoint request
    WEB_METRICS.endpoint_requests.add(
        1,
        &[otel_common::opentelemetry::KeyValue::new(
            "endpoint",
            cmd.to_string(),
        )],
    );

    let mut ret = result.instrument(span.clone()).await.map_or_else(
        |e| {
            log::error!("Error running command {}: {:?}", cmd, e);
            span.set_status(opentelemetry::trace::Status::error(e.to_string()));
            HttpResponse::InternalServerError().body(e.to_string())
        },
        |r| HttpResponse::Ok().json(r),
    );
    add_trace_id_header(&mut ret, trace_id);

    ret
}

fn get_trace_id(span: &tracing::Span) -> Option<TraceId> {
    // Each of the first two creates some sort of temporary value/guard, so we need to split
    // this long chain of calls into multiple lines.
    let ctx = span.context();
    let ctx_span = ctx.span();
    let ctx_span_context = ctx_span.span_context();
    if ctx_span_context.is_valid() {
        Some(ctx_span_context.trace_id())
    } else {
        None
    }
}

fn add_trace_id_header(ret: &mut HttpResponse, trace_id: Option<TraceId>) {
    if let Some(trace_id) = trace_id {
        ret.headers_mut().insert(
            actix_web::http::header::HeaderName::from_static("x-bt-internal-trace-id"),
            trace_id.to_string().parse().unwrap(),
        );
    }
}

struct HeaderMapExtractor<'a>(&'a actix_web::http::header::HeaderMap);
impl<'a> opentelemetry::propagation::Extractor for HeaderMapExtractor<'a> {
    fn get(&self, k: &str) -> Option<&str> {
        self.0.get(k).and_then(|v| v.to_str().ok())
    }
    fn keys(&self) -> Vec<&str> {
        self.0.keys().map(|k| k.as_str()).collect()
    }
}

fn make_request_span(req: &HttpRequest, name: &'static str) -> tracing::Span {
    let context = opentelemetry::global::get_text_map_propagator(|p| {
        p.extract(&HeaderMapExtractor(req.headers()))
    });

    // We have to do this tomfoolery to support both Rust tracing and OTEL's span stuff
    let span = tracing::info_span!("http", otel.name = name);
    span.set_parent(context);

    span
}

#[get("/")]
async fn ping() -> impl Responder {
    HttpResponse::Ok().body("pong")
}

#[get("/version")]
async fn version(req: HttpRequest, state: web::Data<Arc<WebState>>) -> impl Responder {
    wrap_response(
        make_request_span(&req, "version"),
        "version",
        serde_json::Value::Null,
        state.get_ref().clone(),
        async move {
            Ok(json!({
                "commit": crate::GIT_COMMIT,
            }))
        },
    )
    .await
}

#[post("/wal/insert")]
async fn wal_insert(
    req: HttpRequest,
    payload: web::Json<wal::InsertWalArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "wal/insert"),
        "wal/insert",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        wal::insert_wal(state.get_ref().state.clone(), payload.into_inner()),
    )
    .await
}

#[post("/wal/process")]
async fn wal_process(
    req: HttpRequest,
    payload: web::Json<wal::ProcessWalInputArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "wal/process"),
        "wal/process",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            wal::process_wal(
                state.get_ref().state.clone(),
                wal::ProcessWalFullArgs {
                    process_opts: state.get_ref().opts.process_wal_options.clone(),
                    process_input: payload.into_inner(),
                },
            )
            .await
        },
    )
    .await
}

#[post("/wal/compact")]
async fn wal_compact(
    req: HttpRequest,
    payload: web::Json<wal::CompactWalInputArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "wal/compact"),
        "wal/compact",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            if payload.queue {
                if payload.try_acquire
                    && payload.start_xact_id.is_none()
                    && payload.end_xact_id.is_none()
                    && payload.segment_id_args.object_id.is_none()
                    && !payload.segment_id_args.all
                {
                    // If this is a default compaction, we can just add the segments to the compaction loop.
                    match state
                        .get_ref()
                        .compaction_loop
                        .add_segments(
                            payload
                                .segment_id_args
                                .segment_ids
                                .clone()
                                .into_iter()
                                .map(|id| (id, None)),
                        ) {
                        Ok(_) => Ok(util::Value::Null),
                        Err(_) => {
                            util::anyhow::bail!(
                                "Failed to add segments to compaction loop. At capacity. Slow down!"
                            );
                        }
                    }
                } else {
                    util::anyhow::bail!(
                        "To queue a compaction, `try_acquire` must be true, `all` must be false, and `start_xact_id`, `end_xact_id`, `object_id` must be null"
                    );
                }
            } else {
                let cmd = crate::Commands::Wal(wal::WalCommands::Compact(CLIArgs {
                    base: state.get_ref().state.base_args.clone(),
                    args: wal::CompactWalFullArgs {
                        compact_wal_opts: state.get_ref().opts.compact_wal_options.clone(),
                        compact_wal_input: payload.into_inner(),
                    },
                }));
                state
                    .get_ref()
                    .state
                    .command_proc
                    .as_ref()
                    .unwrap()
                    .run_command(cmd)
                    .await
            }
        },
    )
    .await
}

#[post("/wal/cat")]
async fn wal_cat(
    req: HttpRequest,
    payload: web::Json<wal::CatWalArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "wal/cat"),
        "wal/cat",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        wal::cat_wal(state.get_ref().state.clone(), payload.into_inner()),
    )
    .await
}

#[post("/wal/status")]
async fn wal_status(
    req: HttpRequest,
    payload: web::Json<wal::StatusArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "wal/status"),
        "wal/status",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        wal::status(state.get_ref().state.clone(), payload.into_inner()),
    )
    .await
}

#[post("/index/merge")]
async fn index_merge(
    req: HttpRequest,
    payload: web::Json<index::MergeIndexInputArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "index/merge"),
        "index/merge",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            // Special case the merge command itself to use the optimized merge page size.
            let mut base_args = state.get_ref().state.base_args.clone();
            base_args.file_cache_opts.memory_limit = state
                .get_ref()
                .opts
                .optimize_object_tuning_opts
                .merge_memory_limit;
            base_args.file_cache_opts.page_size = state
                .get_ref()
                .opts
                .optimize_object_tuning_opts
                .merge_page_size;

            let cmd = crate::Commands::Index(index::IndexCommands::Merge(CLIArgs {
                base: base_args,
                args: index::MergeIndexFullArgs {
                    merge_input: payload.into_inner(),
                    writer_opts: state.get_ref().opts.compact_wal_options.writer_opts.clone(),
                    process_wal_opts: state.get_ref().opts.process_wal_options.clone(),
                },
            }));
            state
                .state
                .command_proc
                .as_ref()
                .unwrap()
                .run_command(cmd)
                .await
        },
    )
    .await
}

#[post("/index/delete")]
async fn index_delete(
    req: HttpRequest,
    payload: web::Json<index::DeleteIndexArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "index/delete"),
        "index/delete",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            let cmd = crate::Commands::Index(index::IndexCommands::Delete(CLIArgs {
                base: state.get_ref().state.base_args.clone(),
                args: payload.into_inner(),
            }));
            state
                .state
                .command_proc
                .as_ref()
                .unwrap()
                .run_command(cmd)
                .await
        },
    )
    .await
}

#[post("/index/optimize")]
async fn index_optimize(
    req: HttpRequest,
    payload: web::Json<index::OptimizeIndexInput>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "index/optimize"),
        "index/optimize",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            let cmd = crate::Commands::Index(index::IndexCommands::Optimize(CLIArgs {
                base: state.get_ref().state.base_args.clone(),
                args: index::OptimizeIndexFullArgs {
                    optimize_input: payload.into_inner(),
                    optimize_options: OptimizeObjectOptions {
                        compact_wal_opts: state.get_ref().opts.compact_wal_options.clone(),
                        merge_opts: MergeOpts::default(),
                        process_wal_opts: state.get_ref().opts.process_wal_options.clone(),
                        tuning_opts: state.get_ref().opts.optimize_object_tuning_opts.clone(),
                    },
                },
            }));
            state
                .state
                .command_proc
                .as_ref()
                .unwrap()
                .run_command(cmd)
                .await
        },
    )
    .await
}

#[post("/vacuum/segment_wal")]
async fn vacuum_segment_wal(
    req: HttpRequest,
    payload: web::Json<vacuum::VacuumSegmentWalArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "vacuum/segment_wal"),
        "vacuum/segment_wal",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            vacuum::run_vacuum_segment_wal(state.get_ref().state.clone(), payload.into_inner())
                .await
        },
    )
    .await
}

#[post("/vacuum/index_stateless")]
async fn vacuum_index_stateless(
    req: HttpRequest,
    payload: web::Json<vacuum::VacuumIndexStatelessArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "vacuum/index_stateless"),
        "vacuum/index_stateless",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            vacuum::run_vacuum_index_stateless(
                state.get_ref().state.clone(),
                vacuum::VacuumIndexStatelessFullArgs {
                    args: payload.into_inner(),
                    options: state
                        .get_ref()
                        .opts
                        .vacuum_options
                        .vacuum_index_options
                        .clone(),
                },
            )
            .await
        },
    )
    .await
}

#[post("/vacuum/index")]
async fn vacuum_index(
    req: HttpRequest,
    payload: web::Json<vacuum::VacuumIndexArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "vacuum/index"),
        "vacuum/index",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            vacuum::run_vacuum_index(
                state.get_ref().state.clone(),
                vacuum::VacuumIndexFullArgs {
                    args: payload.into_inner(),
                    options: state
                        .get_ref()
                        .opts
                        .vacuum_options
                        .vacuum_index_options
                        .clone(),
                },
            )
            .await
        },
    )
    .await
}

#[post("/retention/time_based")]
async fn time_based_retention(
    req: HttpRequest,
    payload: web::Json<retention::TimeBasedRetentionArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "retention/time_based"),
        "retention/time_based",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            retention::run_time_based_retention(
                state.get_ref().state.clone(),
                &retention::TimeBasedRetentionFullArgs {
                    args: payload.into_inner(),
                    options: state
                        .get_ref()
                        .opts
                        .time_based_retention_worker_options
                        .time_based_retention_options
                        .clone(),
                },
            )
            .await
        },
    )
    .await
}

#[post("/btql/query")]
async fn query(
    req: HttpRequest,
    payload: web::Json<btql::QueryArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    let span = make_request_span(&req, "btql/query");
    let _guard = span.enter();

    let trace_id = get_trace_id(&span);
    let query_start = std::time::Instant::now();

    // Record endpoint request
    WEB_METRICS.endpoint_requests.add(
        1,
        &[otel_common::opentelemetry::KeyValue::new(
            "endpoint",
            "btql/query",
        )],
    );

    let pid = state
        .get_ref()
        .state
        .process_list
        .start("btql/query", serde_json::to_value(&payload).unwrap());

    let mut payload = payload.into_inner();
    // Incorporate the legacy param 'skip_unprocessed_wal_entries' into the
    // index_wal_reader_opts.
    if let Some(skip_unprocessed_wal_entries) = payload.skip_unprocessed_wal_entries {
        payload
            .interpreter_opts
            .index_wal_reader_opts
            .skip_realtime_wal_entries = Some(skip_unprocessed_wal_entries);
    }
    payload =
        payload.incorporate_index_wal_reader_opts(&state.get_ref().opts.index_wal_reader_opts);
    if let Some(schema) = state.get_ref().state.default_logical_schema.as_ref() {
        payload.bind.schema = schema.clone();
    }

    let include_plan = payload.include_plan;
    let should_stream = payload.stream;
    let streaming_query_buffer_size = state.get_ref().opts.streaming_query_buffer_size;
    let query_timeout_seconds = state.get_ref().opts.query_timeout_seconds;

    // Use the server-defined timeout.
    payload.interpreter_opts.query_timeout_seconds = payload
        .interpreter_opts
        .query_timeout_seconds
        .min(query_timeout_seconds);

    let executor = match state
        .get_ref()
        .state
        .executor_pool
        .as_ref()
        .unwrap()
        .get()
        .await
    {
        Ok(executor) => executor,
        Err(e) => {
            log::error!(
                "Failed to get thread pool for executing query (likely overloaded): {}",
                e
            );
            span.set_status(opentelemetry::trace::Status::error(e.to_string()));
            WEB_METRICS.query_errors.add(1, &[]);
            return HttpResponse::InternalServerError()
                .body("Failed to get thread pool for executing query (likely overloaded)");
        }
    };

    let result = match btql::run_query(
        state.get_ref().state.clone(),
        executor.as_ref().clone(),
        payload,
    ) {
        Ok(result) => result,
        // BadRequest plays more nicely with undici, and will propagate rather than returning a TypeError
        Err(e) => {
            span.set_status(opentelemetry::trace::Status::error(e.to_string()));
            WEB_METRICS.query_errors.add(1, &[]);
            return HttpResponse::BadRequest().body(e.to_string());
        }
    };

    let mut result = match result {
        btql::QueryResult::Plan(plan) => HttpResponse::Ok().json(json!({
            "plan": plan,
        })),
        btql::QueryResult::Empty => {
            WEB_METRICS.query_rows_returned.add(0, &[]);
            HttpResponse::Ok().json(json!({
                "rows": vec![] as Vec<serde_json::Value>,
                "errors": vec![] as Vec<String>,
                "cursor": null,
            }))
        }
        btql::QueryResult::Stream(mut stream, ctx, _dir) => {
            if should_stream {
                finish_query_stream(
                    stream,
                    ctx,
                    include_plan,
                    streaming_query_buffer_size,
                    span.clone(),
                    pid,
                    state.opts.slow_query_log_threshold_seconds,
                    query_start,
                )
            } else {
                let mut rows = vec![];
                let mut errors = vec![];
                while let Some(row) = stream.next().await {
                    match row {
                        Ok(row) => rows.push(row),
                        Err(e) => errors.push(e.to_string()),
                    };
                }

                let realtime_state = {
                    let state = ctx.state.load();
                    state
                        .as_ref()
                        .map(|state| state.index_wal_reader.realtime_state().clone())
                };
                let freshness_state = {
                    let state = ctx.state.load();
                    state
                        .as_ref()
                        .map(|state| state.index_wal_reader.freshness_state().clone())
                };
                let tracer = ctx.finish();
                let traced_tree = tracer.into_tree();
                if log::log_enabled!(log::Level::Info) && !suppress_verbose_info() {
                    log::info!("Query plan:\n{}", traced_tree.format_tree().unwrap());
                }

                if errors.len() > 0 {
                    span.set_status(opentelemetry::trace::Status::error(errors.join("\n\n")));
                    WEB_METRICS.query_errors.add(errors.len() as u64, &[]);
                }

                let query_duration = query_start.elapsed();
                WEB_METRICS
                    .query_latency
                    .record(query_duration.as_millis() as u64, &[]);
                WEB_METRICS.query_rows_returned.add(rows.len() as u64, &[]);

                HttpResponse::Ok().json(json!({
                    "rows": rows,
                    "errors": errors,
                    "cursor": Into::<serde_json::Value>::into(ctx.get_cursor()),
                    "plan": if include_plan {
                        Some(traced_tree.format_tree().unwrap())
                    } else {
                        None
                    },
                    "realtime_state": realtime_state,
                    "freshness_state": freshness_state,
                }))
            }
        }
    };

    add_trace_id_header(&mut result, trace_id);
    result
}

fn finish_query_stream(
    mut stream: tracing::Instrumented<::query::interpreter::RowStream<'static>>,
    ctx: Arc<::query::interpreter::InterpreterContext>,
    include_plan: bool,
    streaming_query_buffer_size: usize,
    span: tracing::Span,
    pid: ProcessGuard,
    log_threshold_seconds: usize,
    query_start: std::time::Instant,
) -> HttpResponse {
    let (tx, rx) = mpsc::channel::<Result<Bytes>>(streaming_query_buffer_size);

    let span_clone = span.clone();
    tokio::spawn(
        async move {
            let start_time = std::time::Instant::now();

            // Move the pid to the background thread to keep it alive until the query is finished.
            let _pid = pid;
            let mut errors = vec![];
            let mut row_count = 0u64;
            while let Some(row) = stream.next().await {
                let line = match row {
                    Ok(row) => {
                        row_count += 1;
                        json!({"row": row})
                    }
                    Err(e) => {
                        errors.push(e.to_string());
                        json!({"error": e.to_string()})
                    }
                };
                let line_string = serde_json::to_string(&line)
                    .expect("Failed to serialize query result row to JSON");
                match tx
                    .send(Ok(web::Bytes::from(format!("{}\n", line_string))))
                    .await
                {
                    Ok(_) => (),
                    Err(e) => {
                        log::error!("Failed to send query result row to channel: {:?}", e);
                        break;
                    }
                }
            }

            let realtime_state = {
                let state = ctx.state.load();
                state
                    .as_ref()
                    .map(|state| state.index_wal_reader.realtime_state().clone())
            };
            let freshness_state = {
                let state = ctx.state.load();
                state
                    .as_ref()
                    .map(|state| state.index_wal_reader.freshness_state().clone())
            };
            let tracer = ctx.finish();
            let traced_tree = tracer.into_tree();
            if log::log_enabled!(log::Level::Debug) {
                log::debug!("Query plan:\n{}", traced_tree.format_tree().unwrap());
            } else if log::log_enabled!(log::Level::Info)
                && !suppress_verbose_info()
                && start_time.elapsed().as_secs() as usize > log_threshold_seconds
            {
                log::info!("Slow query plan:\n{}", traced_tree.format_tree().unwrap());
            }

            span.add_event(
                "execution_plan",
                vec![opentelemetry::KeyValue::new(
                    "",
                    traced_tree.format_tree().unwrap(),
                )],
            );

            if errors.len() > 0 {
                span.set_status(opentelemetry::trace::Status::error(errors.join("\n\n")));
                WEB_METRICS.query_errors.add(errors.len() as u64, &[]);
            }

            // Record streaming query metrics
            let query_duration = query_start.elapsed();
            WEB_METRICS
                .query_latency
                .record(query_duration.as_millis() as u64, &[]);
            WEB_METRICS.query_rows_returned.add(row_count, &[]);

            let info = json!({ "info": json!({
                    "cursor": Into::<serde_json::Value>::into(ctx.get_cursor()),
                    "plan": if include_plan {
                        Some(traced_tree.format_tree().unwrap())
                    } else {
                        None
                    },
                    "realtime_state": realtime_state,
                    "freshness_state": freshness_state,
                }),
            });
            let line_string =
                serde_json::to_string(&info).expect("Failed to serialize info line to JSON");
            tx.send(Ok(web::Bytes::from(format!("{}\n", line_string))))
                .await
                .expect("Failed to send query result info to channel");
        }
        .instrument(span_clone),
    );

    HttpResponse::Ok()
        .content_type("application/x-ndjson")
        .streaming(ReceiverStream::new(rx))
}

#[get("/processes")]
async fn processes(state: web::Data<Arc<WebState>>) -> impl Responder {
    let processes = state.get_ref().state.process_list.list(true);
    let now = std::time::Instant::now();
    let compaction_queue_size = state.get_ref().compaction_loop.queue_size();

    let mut process_values = processes
        .into_iter()
        .map(|p| {
            let start_instant = std::time::SystemTime::now()
                .checked_sub(now.duration_since(p.start_time))
                .unwrap();
            let start_datetime = chrono::DateTime::<chrono::Utc>::from(start_instant);

            let duration = match p.end_time {
                Some(end_time) => end_time.duration_since(p.start_time),
                None => now.duration_since(p.start_time),
            };

            // Filter out model_costs from args if present
            let filtered_args = if let Some(obj) = p.args.as_object() {
                let mut filtered = obj.clone();
                filtered.remove("model_costs");
                serde_json::Value::Object(filtered)
            } else {
                p.args
            };

            (
                duration,
                json!({
                    "pid": p.pid,
                    "cmd": p.cmd,
                    "args": filtered_args,
                    "start_time": start_datetime.to_rfc3339(),
                    "duration": format!("{:?}", duration),
                }),
            )
        })
        .collect::<Vec<_>>();

    // Sort by duration (longest first)
    process_values.sort_by(|a, b| b.0.cmp(&a.0));

    let process_values: Vec<_> = process_values.into_iter().map(|(_, json)| json).collect();

    HttpResponse::Ok().json(json!({
        "processes": process_values,
        "compaction_queue_size": compaction_queue_size,
    }))
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct ObjectInfoArgs {
    object_id: String,
}

#[post("/object_info")]
async fn object_info(
    req: HttpRequest,
    payload: web::Json<ObjectInfoArgs>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    let object_id = payload.into_inner().object_id;
    let app_state = state.get_ref().state.clone();
    wrap_response(
        make_request_span(&req, "object/info"),
        "object/info",
        serde_json::to_value(&object_id).unwrap(),
        state.get_ref().clone(),
        async move {
            let object_id_owned: FullObjectIdOwned = object_id.parse()?;
            let object_id = object_id_owned.as_ref();
            let object_id_arr = [object_id];
            let (last_processed_xact_id, segment_ids) = join!(
                app_state
                    .config
                    .global_store
                    .query_object_metadatas(&object_id_arr),
                app_state
                    .config
                    .global_store
                    .list_segment_ids(&object_id_arr, None),
            );
            let last_processed_xact_id = last_processed_xact_id?.remove(0).last_processed_xact_id;
            let segment_ids = segment_ids?.remove(0);
            let (segment_metas, segment_last_processed_xact_ids, last_index_operations, pagination_key_stats) = join!(
                app_state
                    .config
                    .global_store
                    .query_segment_metadatas(&segment_ids),
                app_state
                    .config
                    .global_store.query_segment_wal_entries_xact_id_statistic(
                    &segment_ids,
                    SegmentWalEntriesXactIdStatistic::Max,
                    None,
                ),
                app_state
                    .config
                    .global_store
                    .query_last_index_operations(&segment_ids),
                app_state
                    .config
                    .global_store
                    .query_field_statistics(&segment_ids, &[PAGINATION_KEY_FIELD]),
            );
            let segment_metas = segment_metas?;
            let segment_last_processed_xact_ids = segment_last_processed_xact_ids?;
            let last_index_operations = last_index_operations?;
            let pagination_key_stats = pagination_key_stats?;
            Ok(json!({
                "object_id": object_id,
                "last_processed_xact_id": last_processed_xact_id.map(|x| format!("{}", x)),
                "segments": segment_ids
                    .into_iter()
                    .zip(segment_metas)
                    .zip(segment_last_processed_xact_ids)
                    .zip(last_index_operations)
                    .map(|(((segment_id, meta), last_processed_xact_id), op)| (
                        segment_id,
                        json!({
                            // We can expose more from here in the future, if it's useful
                            "minimum_pagination_key": format!("{}", meta.minimum_pagination_key),
                            "stats": json!({
                                "_pagination_key": pagination_key_stats.get(&segment_id).and_then(|x| x.get(PAGINATION_KEY_FIELD)).map(|x| json!({
                                    "min": format!("{}", PaginationKey(x.min())),
                                    "max": format!("{}", PaginationKey(x.max())),
                                })),
                            }),
                            "num_rows": meta.num_rows,
                            "last_processed_xact_id": last_processed_xact_id.map(|x| format!("{}", x)),
                            "last_compacted_index_meta": meta.last_compacted_index_meta.map(|meta| json!({
                                "xact_id": format!("{}", meta.xact_id),
                                "storage_meta": json!({
                                    "index_settings": meta.tantivy_meta.index_settings,
                                    "segments": meta.tantivy_meta.segments,
                                    "opstamp": meta.tantivy_meta.opstamp,
                                    "payload": meta.tantivy_meta.payload,
                                    "brainstore_meta": meta.tantivy_meta.brainstore_meta,
                                }),
                            })),
                            "last_index_operation": json!(op),
                        }),
                    ))
                    .collect::<BTreeMap<_, _>>(),
            }))
        },
    )
    .await
}

#[cfg(feature = "enable_memprof")]
#[post("/dump_memprof")]
async fn dump_memprof(state: web::Data<Arc<WebState>>) -> impl Responder {
    use util::anyhow::anyhow;

    let resp = async move {
        let memprof_dir = state
            .state
            .base_args
            .telemetry_args
            .memprof_dir
            .as_ref()
            .ok_or_else(|| {
                anyhow!("Memory profiling is not enabled. Set --memprof-dir to enable.")
            })?;
        let heap_profile = crate::memprof::profile_heap().await?;
        crate::memprof::dump_heap_to_file(heap_profile, memprof_dir)
    };
    match resp.await {
        Ok(path) => HttpResponse::Ok().body(format!("Dumped profile to {:?}", path)),
        Err(e) => HttpResponse::BadRequest().body(e.to_string()),
    }
}

#[cfg(not(feature = "enable_memprof"))]
#[post("/dump_memprof")]
async fn dump_memprof() -> impl Responder {
    HttpResponse::BadRequest().body("Memory profiling is not enabled.")
}

#[get("/status")]
async fn status(state: web::Data<Arc<WebState>>) -> impl Responder {
    let app_state = &state.get_ref().state;
    let span = tracing::info_span!("status");
    let _guard = span.enter();

    match crate::healthcheck::run_healthcheck(app_state).await {
        Ok(resp) => match resp.get("status").unwrap().as_str() {
            Some("ok") => HttpResponse::Ok().json(resp),
            Some("error") => {
                span.set_status(opentelemetry::trace::Status::error("service unavailable"));
                HttpResponse::ServiceUnavailable().json(resp)
            }
            _ => {
                log::error!("Status check returned unknown status: {}", resp);
                span.set_status(opentelemetry::trace::Status::error(format!(
                    "unknown status: {}",
                    resp
                )));
                HttpResponse::InternalServerError().json(json!({
                    "error": "unknown status",
                    "details": resp,
                }))
            }
        },
        Err(e) => {
            log::error!("Status check failed: {:#}", e);
            span.set_status(opentelemetry::trace::Status::error(e.to_string()));
            HttpResponse::InternalServerError().json(json!({
                "error": e.to_string(),
            }))
        }
    }
}

#[get("/locks_snapshot")]
async fn locks_snapshot(state: web::Data<Arc<WebState>>) -> impl Responder {
    let app_state = &state.get_ref().state;
    match app_state.config.locks_manager.snapshot_lock_state().await {
        Ok(resp) => HttpResponse::Ok().json(resp),
        Err(e) => {
            log::error!("Locks snapshot failed: {:#}", e);
            HttpResponse::InternalServerError().json(json!({
                "error": e.to_string(),
            }))
        }
    }
}

#[get("/locks_sanity_check")]
async fn locks_sanity_check(state: web::Data<Arc<WebState>>) -> impl Responder {
    let app_state = &state.get_ref().state;

    // Make sure the write-lock semantics work.
    let write_locks_result =
        match locks_sanity_check_writes(app_state.config.locks_manager.clone()).await {
            Ok(_) => "success".to_string(),
            Err(e) => format!("{:#}", e),
        };
    HttpResponse::Ok().json(json!({
        "check_write_locks": write_locks_result,
    }))
}

// We run the locking checks on a separate tokio runtime because for whatever reason spawning it on
// the webserver runtime interacts poorly with the #[instrument] macro.
pub static LOCKS_SANITY_CHECK_RUNTIME: Lazy<std::io::Result<tokio::runtime::Runtime>> =
    Lazy::new(|| {
        tokio::runtime::Builder::new_current_thread()
            .enable_all()
            .build()
    });
const LOCKS_SANITY_CHECK_WRITES_LOCK_NAME: &str = "__locks_sanity_check_writes__";

async fn locks_sanity_check_writes(locks_manager: Arc<dyn GlobalLocksManager>) -> Result<()> {
    let lock_state = Arc::new(Mutex::new(0));
    let runtime = LOCKS_SANITY_CHECK_RUNTIME.as_ref()?;

    // Spawn a task which grabs the write lock and then waits until we have bumped the lock state.
    let writer_task = {
        let locks_manager = locks_manager.clone();
        let lock_state = lock_state.clone();
        runtime.spawn(async move {
            let _guard = locks_manager
                .write(LOCKS_SANITY_CHECK_WRITES_LOCK_NAME)
                .await?;
            *lock_state.lock().await = 1;
            while *lock_state.lock().await == 1 {
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            }
            Ok::<_, util::anyhow::Error>(())
        })
    };

    // Wait for the lock state to reach 1, which means `writer_task` has grabbed the lock.
    while *lock_state.lock().await == 0 {
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }

    // Trying to acquire the write lock should fail.
    let try_write_res = locks_manager
        .try_write(LOCKS_SANITY_CHECK_WRITES_LOCK_NAME)
        .await?;
    let result = if try_write_res.is_some() {
        Err(util::anyhow::anyhow!(
            "Locks sanity check failed: was able to acquire write lock while it was held"
        ))
    } else {
        Ok(())
    };

    // Bump the lock state to 2, which will allow `writer_task` to release the lock.
    *lock_state.lock().await = 2;
    writer_task.await??;

    result
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct WebArgs {
    #[arg(
        short,
        long,
        default_value = "127.0.0.1:8080",
        env = "BRAINSTORE_WEB_LISTEN"
    )]
    pub listen: String,

    #[arg(
        long,
        help = "Enable CORS for the given origin",
        env = "BRAINSTORE_WEB_CORS_ORIGIN"
    )]
    pub cors_origin: Option<String>,
}

async fn web_optimize_loop(web_state: Arc<WebState>, loop_type: OptimizationLoopType) {
    let cmd = crate::Commands::Index(index::IndexCommands::OptimizeLoop(CLIArgs {
        base: web_state.state.base_args.clone(),
        args: index::OptimizeAllObjectsFullArgs {
            input: crate::index::OptimizeAllObjectsInput {
                loop_type,
                ..web_state.opts.optimize_all_objects_input.clone()
            },
            options: OptimizeAllObjectsOptions {
                optimize_opts: OptimizeObjectOptions {
                    compact_wal_opts: web_state.opts.compact_wal_options.clone(),
                    merge_opts: MergeOpts::default(),
                    process_wal_opts: web_state.opts.process_wal_options.clone(),
                    tuning_opts: web_state.opts.optimize_object_tuning_opts.clone(),
                },
                ..Default::default()
            },
        },
    }));

    // Run the command once - launch_background_worker will handle restarts
    match web_state
        .state
        .command_proc
        .as_ref()
        .unwrap()
        .run_command(cmd)
        .await
    {
        Ok(_) => {}
        Err(e) => {
            log::error!("Optimization command failed: {}", e);
            // Re-panic to trigger the restart mechanism
            panic!("Optimization command failed: {}", e);
        }
    }
}

fn launch_vacuum_worker(web_state: Arc<WebState>) {
    let web_state_clone = web_state.clone();
    tokio::spawn(launch_background_worker("vacuum_index_worker", move || {
        let state = web_state_clone.state.clone();
        let options = web_state_clone.opts.vacuum_options.clone();
        async move { crate::vacuum::vacuum_index_worker_task(state, options).await }
    }));
    // Add more vacuum workers here as we implement them.
}

async fn launch_background_worker<F, Fut>(name: &'static str, mut factory: F)
where
    F: FnMut() -> Fut + Send + 'static,
    Fut: Future<Output = ()> + Send + 'static,
{
    loop {
        let future = factory();
        let result = tokio::spawn(async move {
            future.await;
        })
        .await;

        match result {
            Ok(()) => {
                log::warn!("{} worker exited normally, restarting...", name);
            }
            Err(e) => {
                if e.is_panic() {
                    log::error!(
                        "{} worker panicked: {:?}, restarting after 1 second...",
                        name,
                        e
                    );
                } else {
                    log::error!(
                        "{} worker errored: {:?}, restarting after 1 second...",
                        name,
                        e
                    );
                }
                tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
            }
        }
    }
}

fn launch_process_wal_worker(web_state: Arc<WebState>) {
    let web_state_clone = web_state.clone();
    tokio::spawn(launch_background_worker("process_wal", move || {
        let web_state = web_state_clone.clone();
        async move {
            backfill_worker(
                BackfillWorkerInput {
                    config: web_state.state.config.clone(),
                    process_wal_opts: web_state.opts.process_wal_options.clone(),
                    compaction_loop: web_state.compaction_loop.clone(),
                },
                Default::default(),
                web_state.opts.backfill_worker_options.clone(),
            )
            .await
        }
    }));
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct VacuumStatusRequest {
    object_ids: Vec<String>,
    vacuum_index_stale_seconds: Option<i64>,
}

#[post("/vacuum/status")]
async fn vacuum_status(
    req: HttpRequest,
    payload: web::Json<VacuumStatusRequest>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "vacuum/status"),
        "vacuum/status",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            let object_ids: Vec<FullObjectIdOwned> = payload
                .object_ids
                .iter()
                .map(|id| id.parse::<FullObjectIdOwned>())
                .collect::<Result<_>>()?;

            // Unless this param is provided in the request, set it to the currently active value,
            // which is the sum of the deletion grace period and the last written slop time.
            let vacuum_index_stale_seconds =
                payload.vacuum_index_stale_seconds.unwrap_or_else(|| {
                    let opts = &state.opts.vacuum_options.vacuum_index_options;
                    opts.vacuum_index_deletion_grace_period_seconds
                        + opts.vacuum_index_last_written_slop_seconds
                });

            let pg_conn = get_pg_conn(state.get_ref().state.clone())?;
            get_vacuum_status(&pg_conn, Some(object_ids), vacuum_index_stale_seconds).await
        },
    )
    .await
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct VacuumResetStateRequest {
    object_ids: Vec<String>,
    sleep_for_vacuum_index_stale_seconds: Option<bool>,
}

#[post("/vacuum/reset_state")]
async fn vacuum_reset_state(
    req: HttpRequest,
    payload: web::Json<VacuumResetStateRequest>,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    wrap_response(
        make_request_span(&req, "vacuum/reset_state"),
        "vacuum/reset_state",
        serde_json::to_value(&payload).unwrap(),
        state.get_ref().clone(),
        async move {
            let object_ids: Vec<FullObjectIdOwned> = payload
                .object_ids
                .iter()
                .map(|id| id.parse::<FullObjectIdOwned>())
                .collect::<Result<_>>()?;

            if payload
                .sleep_for_vacuum_index_stale_seconds
                .unwrap_or(false)
            {
                // Sleep for enough time that the next vacuum is guaranteed to delete
                // stale files created before the request was received.
                let opts = &state.opts.vacuum_options.vacuum_index_options;
                tokio::time::sleep(tokio::time::Duration::from_secs(
                    (opts.vacuum_index_deletion_grace_period_seconds
                        + opts.vacuum_index_last_written_slop_seconds) as u64,
                ))
                .await;
            }

            let pg_conn = get_pg_conn(state.get_ref().state.clone())?;
            reset_vacuum_state(&pg_conn, Some(object_ids)).await?;

            Ok(json!({"success": true}))
        },
    )
    .await
}

async fn status_reporting_task(
    web_state: Arc<WebState>,
    app_url: String,
    license_key: String,
    resource_json: serde_json::Map<String, util::Value>,
) {
    let mut interval = tokio::time::interval(std::time::Duration::from_secs(300)); // Report every 5 minutes

    loop {
        interval.tick().await;

        let status_value = json!({
            "resource": resource_json,
            "status": crate::healthcheck::run_healthcheck(&web_state.state).await.unwrap_or_else(|e| util::Value::String(format!("error: {:?}", e))),
            "config": crate::healthcheck::get_config_args(&web_state.state, Some(&web_state.opts))
        });

        // Run healthcheck
        match send_status_to_control_plane(&app_url, &license_key, status_value).await {
            Ok(_) => {
                tracing::debug!("Successfully sent status to control plane");
            }
            Err(e) => {
                tracing::error!("Failed to send status to control plane: {:?}", e);
                // Re-panic to trigger restart
                panic!("Failed to send status to control plane: {:?}", e);
            }
        }
    }
}

fn launch_status_reporting_loop(
    web_state: Arc<WebState>,
    app_url: String,
    license_key: String,
    additional_attributes: Vec<otel_common::opentelemetry::KeyValue>,
) {
    let resource = agent::setup_tracing::create_otel_resource(
        "brainstore",
        Some(crate::GIT_COMMIT),
        additional_attributes,
    );

    let resource_json: serde_json::Map<String, util::Value> = resource
        .into_iter()
        .map(|(key, value)| (key.to_string(), util::Value::String(value.to_string())))
        .collect();

    tokio::spawn(launch_background_worker("status_reporting", move || {
        let web_state = web_state.clone();
        let app_url = app_url.clone();
        let license_key = license_key.clone();
        let resource_json = resource_json.clone();
        async move { status_reporting_task(web_state, app_url, license_key, resource_json).await }
    }));
}

async fn send_status_to_control_plane(
    app_url: &str,
    license_key: &str,
    status_data: serde_json::Value,
) -> Result<()> {
    use util::url_util::url_join;

    let url = url_join(app_url, "/api/pulse/status");
    let client = reqwest::Client::new();

    let response = client
        .post(&url)
        .header("Authorization", format!("Bearer {}", license_key))
        .header("Content-Type", "application/json")
        .json(&status_data)
        .send()
        .await?;

    let status_code = response.status();
    if !status_code.is_success() {
        let text = response.text().await.unwrap_or_default();
        util::anyhow::bail!(
            "Failed to send status to control plane: {} - {}",
            status_code.as_str(),
            text
        );
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};

    #[tokio::test]
    async fn test_launch_background_worker_restarts_on_panic() {
        let counter = Arc::new(AtomicUsize::new(0));
        let counter_clone = counter.clone();

        let handle = tokio::spawn(launch_background_worker("test_panic_worker", move || {
            let counter = counter_clone.clone();
            async move {
                let count = counter.fetch_add(1, Ordering::SeqCst);
                if count < 3 {
                    panic!("Test panic #{}", count + 1);
                }
                // After 3 panics, just exit normally
            }
        }));

        // Give it some time to run and restart a few times
        tokio::time::sleep(tokio::time::Duration::from_secs(4)).await;

        // Should have run at least 3 times (and panicked 3 times)
        assert!(counter.load(Ordering::SeqCst) >= 3);

        // Cancel the task
        handle.abort();
    }

    #[tokio::test]
    async fn test_launch_background_worker_restarts_on_normal_exit() {
        let counter = Arc::new(AtomicUsize::new(0));
        let counter_clone = counter.clone();

        let handle = tokio::spawn(launch_background_worker("test_exit_worker", move || {
            let counter = counter_clone.clone();
            async move {
                counter.fetch_add(1, Ordering::SeqCst);
                // Just exit normally
            }
        }));

        // Give it some time to run and restart a few times
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

        // Should have run multiple times
        assert!(counter.load(Ordering::SeqCst) >= 2);

        // Cancel the task
        handle.abort();
    }

    #[tokio::test]
    async fn test_launch_background_worker_with_async_work() {
        let counter = Arc::new(AtomicUsize::new(0));
        let should_exit = Arc::new(AtomicBool::new(false));
        let counter_clone = counter.clone();
        let should_exit_clone = should_exit.clone();

        let handle = tokio::spawn(launch_background_worker("test_async_worker", move || {
            let counter = counter_clone.clone();
            let should_exit = should_exit_clone.clone();
            async move {
                let current_count = counter.load(Ordering::SeqCst);
                // Only increment on fresh starts
                if current_count < 2 {
                    counter.fetch_add(1, Ordering::SeqCst);
                }

                // Simulate some async work
                let mut interval = tokio::time::interval(tokio::time::Duration::from_millis(100));
                while !should_exit.load(Ordering::SeqCst) {
                    interval.tick().await;
                }
            }
        }));

        // Wait a bit to ensure it's running
        tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
        assert_eq!(counter.load(Ordering::SeqCst), 1);

        // Signal it to exit
        should_exit.store(true, Ordering::SeqCst);

        // Wait for it to restart
        tokio::time::sleep(tokio::time::Duration::from_millis(400)).await;
        let final_count = counter.load(Ordering::SeqCst);
        assert!(
            final_count >= 2,
            "Expected at least 2 runs, got {}",
            final_count
        );

        // Cancel the task
        handle.abort();
    }

    #[tokio::test]
    async fn test_launch_background_worker_panic_delay() {
        let counter = Arc::new(AtomicUsize::new(0));
        let counter_clone = counter.clone();
        let start_time = std::time::Instant::now();

        let handle = tokio::spawn(launch_background_worker("test_delay_worker", move || {
            let counter = counter_clone.clone();
            async move {
                let count = counter.fetch_add(1, Ordering::SeqCst);
                if count < 2 {
                    panic!("Test panic to trigger delay");
                }
            }
        }));

        // Wait for 2 panics with delays
        while counter.load(Ordering::SeqCst) < 2 {
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }

        // Wait a bit more to ensure the delay has been applied
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

        let elapsed = start_time.elapsed();

        // Should have taken at least 1 second (1 panic with 1 second delay)
        // We check for 1 second instead of 2 because the timing might be slightly off
        assert!(
            elapsed >= tokio::time::Duration::from_millis(900),
            "Expected at least 900ms delay, got {:?}",
            elapsed
        );

        // Cancel the task
        handle.abort();
    }
}
