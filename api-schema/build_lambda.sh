#!/bin/bash

# https://docs.aws.amazon.com/lambda/latest/dg/python-package.html#python-package-create-dependencies
pip install \
    --platform manylinux2014_x86_64 \
    --target=package \
    --implementation cp \
    --python-version 3.13 \
    --only-binary=:all: --upgrade \
    -r lambda_function_requirements.txt

rm -f deployment.zip
zip deployment.zip lambda_function.py
zip -r deployment.zip migrations
if [ -d draft-migrations ]; then
    zip -r deployment.zip draft-migrations
fi
cd package
zip -r ../deployment.zip .
