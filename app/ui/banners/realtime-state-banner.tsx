import { BrainstoreObjectConfiguration } from "#/app/app/[org]/p/[project]/brainstore/[object]/brainstore-object-configuration";
import { type RealtimeState } from "@braintrust/local/app-schema";
import { useState } from "react";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { InfoBanner } from "#/ui/info-banner";

export function isRealtimeStateExhausted(
  realtimeState: RealtimeState | undefined,
) {
  return realtimeState?.type && realtimeState.type.includes("exhausted");
}

export function RealtimeStateBanner({
  realtimeState,
  objectType,
  objectId,
}: {
  realtimeState?: RealtimeState;
  objectType: DataObjectType;
  objectId: string | null;
}) {
  const [showModal, setShowModal] = useState(false);
  if (
    !realtimeState ||
    realtimeState.type === "disabled" ||
    realtimeState.type === "on" ||
    !objectId
  ) {
    return null;
  }

  return (
    <>
      <InfoBanner className="my-0 w-full">
        <span className="flex justify-between gap-4">
          <span>Some rows are still being processed</span>
          <button
            onClick={() => {
              setShowModal(true);
            }}
            className="cursor-pointer text-right font-medium hover:underline"
          >
            Check backfill status
          </button>
        </span>
      </InfoBanner>
      {showModal && (
        <BrainstoreObjectConfiguration
          objectId={`${objectType}:${objectId}`}
          onOpenChange={setShowModal}
        />
      )}
    </>
  );
}
