export const LAST_VISITED_ORG_COOKIE = "last-visited-org";

// Client-side function to set the last visited org cookie
export function setLastVisitedOrg(orgName: string): void {
  const d = new Date();
  d.setTime(d.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
  const expires = "expires=" + d.toUTCString();

  document.cookie = `${LAST_VISITED_ORG_COOKIE}=${encodeURIComponent(orgName)};${expires};path=/`;
}

// Client-side function to get the last visited org from cookies
export function getLastVisitedOrgClient(): string | null {
  if (typeof document === "undefined") {
    return null;
  }

  const cookies = document.cookie.split(";");
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split("=");
    if (name === LAST_VISITED_ORG_COOKIE) {
      return decodeURIComponent(value);
    }
  }
  return null;
}
