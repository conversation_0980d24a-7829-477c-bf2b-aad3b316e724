"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useAnalytics } from "#/ui/use-analytics";
import { Button } from "#/ui/button";
import { useCreateDatasetDialog } from "#/ui/dialogs/create-dataset";
import { type SearchSpec, buildDefaultOrderBy } from "#/utils/search/search";
import { useViewStates, type ViewParams } from "#/utils/view/use-view";
import { Views } from "#/ui/views";
import AppliedFilters from "#/ui/applied-filters";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { useTableSelection } from "#/ui/table/useTableSelection";
import { VizQuery } from "#/ui/viz-query";
import { useArrowAPI } from "#/utils/btapi/btapi";
import { useMaterializedArrow } from "#/utils/duckdb";
import { useOrg } from "#/utils/user";
import { Field, Int, Schema, Utf8 } from "apache-arrow";
import { Database, Plus, Trash } from "lucide-react";
import { type ClientOptions } from "openai";
import { useCallback, useContext, useMemo, useRef } from "react";
import { useEntityBatchActions } from "../../useEntityBatchActions";
import { useProjectRowEvents } from "../experiments-formatters";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import {
  type BTQLTableDefinition,
  compileClauseToSQL,
  useClauseChecker,
} from "#/utils/search-btql";
import { z } from "zod";
import { runAISearch } from "#/utils/ai-search/actions/events";
import { AccessFailed } from "#/ui/access-failed";
import { isEmpty } from "#/utils/object";
import type { FormatterMap } from "#/ui/field-to-column";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { useBtql } from "#/utils/btql/btql";
import * as Query from "#/utils/btql/query-builder";
import { getRecentMetadataEntries } from "../experiments/clientpage";
import { ProjectListLayout } from "../project-list-layout";
import { decodeURIComponentPatched } from "#/utils/url";
import { zodToLogicalSchema } from "#/utils/zod-to-logical-schema";
import {
  DefaultFormatter,
  DefaultWithAggregationFormatter,
} from "#/ui/table/formatters/default-formatter";
import { GroupKeyFormatterWithCell } from "#/ui/table/formatters/group-key-formatter";
import { useTableGroupingControl } from "#/ui/table/grouping/controls";
import {
  BT_GROUP_KEY,
  BT_IS_GROUP,
  nullValueExpr,
  rowGroupingQuery,
} from "#/ui/table/grouping/queries";

import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { DuckDBTypeHints } from "#/utils/schema";
import { type FormatterProps } from "#/ui/arrow-table";
import { cn } from "#/utils/classnames";
import { getDatasetLink } from "./[dataset]/getDatasetLink";
import { useRouter } from "next/navigation";

export interface Params {
  org: string;
  project: string;
}

const emptyDatasetsSummarySchema = new Schema([
  Field.new({ name: "dataset_id", type: new Utf8() }),
  Field.new({ name: "last_updated", type: new Utf8() }),
  Field.new({ name: "num_examples", type: new Int(true, 64) }),
]);

const logicalSchema = z.strictObject({
  name: z.string(),
  description: z.string().optional(),
  last_updated: z.string().datetime(),
  num_examples: z.number(),
  metadata: z.record(z.any()).optional(),
  id: z.string(),
});

const datasetMetadataDef: BTQLTableDefinition = {
  logical: zodToLogicalSchema(logicalSchema),
  physical: {
    columns: {
      name: { path: ["name"], type: { type: "varchar" } },
      description: { path: ["description"], type: { type: "varchar" } },
      last_updated: { path: ["last_updated"], type: { type: "varchar" } },
      num_examples: { path: ["num_examples"], type: { type: "bigint" } },
      metadata: { path: ["metadata"], type: { type: "json" } },
      id: { path: ["id"], type: { type: "varchar" } },
    },
  },
};

export default function ClientPage({ params }: { params: Params }) {
  const orgName = decodeURIComponentPatched(params.org);
  const projectName = decodeURIComponentPatched(params.project);

  const org = useOrg();

  const numRowsRef = useRef(0);

  const {
    selectedRows: rowSelectionDatasets,
    setSelectedRows: setRowSelectionDatasets,
    getSelectedRowsWithData: getSelectedRowsWithDataDatasets,
    selectedRowsNumber: selectedRowsNumberDatasets,
    deselectAllTableRows: deselectAllTableRowsDatasets,
    tableRef: tableRefDatasets,
  } = useTableSelection();

  const { projectId, datasetsReady, datasetsTable, mutateDatasets } =
    useContext(ProjectContext);

  const { clauseChecker } = useClauseChecker(datasetMetadataDef);
  const viewParams: ViewParams | undefined = projectId
    ? {
        objectType: "project",
        objectId: projectId,
        viewType: "datasets",
      }
    : undefined;
  const pageIdentifier = `project-datasets-list-${projectId}`;
  const viewProps = useViewStates({
    viewParams,
    clauseChecker,
    pageIdentifier,
  });

  useAnalytics({
    page: projectId
      ? {
          category: "project",
          props: { project_id: projectId },
        }
      : null,
  });

  const { datasetIds: recentDatasetIds } = useRecentDatasets();
  const { projectDatasets } = useContext(ProjectContext);

  const btqlSummary = useBtql({
    name: "Dataset summary query",
    query: useMemo(
      () =>
        projectDatasets
          ? {
              from: Query.from(
                "dataset",
                projectDatasets
                  .filter((d) => recentDatasetIds.includes(d.id))
                  .map((d) => d.id),
              ),
              measures: [
                { alias: "last_updated", expr: { btql: "max(created)" } },
                { alias: "num_examples", expr: { btql: "count(1)" } },
              ],
              dimensions: [
                { alias: "dataset_id", expr: { btql: "dataset_id" } },
              ],
            }
          : null,
      [projectDatasets, recentDatasetIds],
    ),
    disableLimit: true,
    brainstoreRealtime: true,
  });

  const projectIdParams = useMemo(
    () =>
      projectId && btqlSummary.unsupported
        ? {
            project_id: projectId,
          }
        : null,
    [btqlSummary.unsupported, projectId],
  );

  const { actions: deleteDatasetsActions, modals: deleteDatasetsModal } =
    useEntityBatchActions({
      entityType: "dataset",
      onUpdate: () => {
        mutateDatasets();
        deselectAllTableRowsDatasets();
      },
      entityName: projectName,
    });

  const router = useRouter();
  const { modal: createDatasetModal, open: openCreateDatasetDialog } =
    useCreateDatasetDialog({
      orgId: org.id,
      projectName: projectName,
      onSuccessfulCreate: ({ datasetName }) => {
        mutateDatasets();
        router.push(
          getDatasetLink({
            orgName,
            projectName,
            datasetName,
          }),
        );
      },
    });

  const { data: datasetSummary } = useArrowAPI({
    name: "Dataset summary query",
    endpoint: projectIdParams && "datasets",
    params: projectIdParams,
    schema: emptyDatasetsSummarySchema,
  });
  const { refreshed: datasetSummaryReady, name: datasetSummaryTable } =
    useMaterializedArrow(
      `datasets_summary_${projectId}`,
      btqlSummary.unsupported ? datasetSummary : (btqlSummary.data ?? null),
    );

  const sizeConstraintsMap = useMemo(
    () => ({
      name: {
        minSize: 250,
      },
      description: {
        minSize: 250,
      },
      last_updated: {
        minSize: 250,
      },
      source: {
        minSize: 180,
      },
    }),
    [],
  );

  const buildDatasetsQuery = useCallback(
    (s: SearchSpec) => {
      const matchFilter =
        s.match && s.match.length > 0
          ? s.match
              .map(
                (m) =>
                  `contains(lower(datasets.name), ${singleQuote(
                    m.text.toLowerCase(),
                  )})`,
              )
              .join(" AND ")
          : "TRUE";

      const filters: string[] =
        s.filter?.map((f) => compileClauseToSQL(f)) ?? [];
      const where = filters.length
        ? `${filters.map((f) => `(${f})`).join(" AND ")}`
        : "true";

      const orderBy = buildDefaultOrderBy(s);

      return (
        datasetsTable &&
        datasetSummaryTable &&
        `
        SELECT * FROM (
        SELECT
          datasets.name,
          datasets.description,
          datasets_summary.* EXCLUDE (dataset_id),
          datasets.metadata,
          datasets.id,
        FROM "${datasetsTable}" datasets
        LEFT JOIN "${datasetSummaryTable}" datasets_summary ON datasets.id = datasets_summary.dataset_id
        WHERE datasets.project_id = '${projectId}'
        AND ${matchFilter}
        ${orderBy ? `ORDER BY ${orderBy}` : ""}
        ) base WHERE ${where}
        `
      );
    },
    [datasetsTable, datasetSummaryTable, projectId],
  );

  const datasetsSignals = useMemo(
    () => [datasetSummaryReady, datasetsReady],
    [datasetSummaryReady, datasetsReady],
  );

  const {
    selectComponent,
    tableGrouping,
    groupAggregationTypes,
    setGroupAggregationType,
  } = useTableGroupingControl({
    baseQuery: datasetsTable
      ? `SELECT * FROM ${doubleQuote(datasetsTable)} WHERE project_id = '${projectId}'`
      : null,
    signals: [datasetsReady],
    tableIdentifier: `datasets-${projectId}`,
    viewProps,
  });

  const datasetsQuery = useMemo(
    () => buildDatasetsQuery(viewProps.search),
    [buildDatasetsQuery, viewProps.search],
  );

  const groupQuery = rowGroupingQuery({
    tableGrouping,
    baseQuery: datasetsQuery,
    groupColumnName: "name",
    columnsFn: (columnName) =>
      Object.keys(logicalSchema.shape)
        .map((f: string) => {
          if (f === columnName) {
            return `COALESCE(${BT_GROUP_KEY}, NULL) AS ${columnName}`;
          }
          if (f === "last_updated") {
            return "MAX(last_updated) AS last_updated";
          }
          if (f === "num_examples") {
            return `${groupAggregationTypes.metrics ?? "SUM"}(${f}) AS ${f}`;
          }
          return nullValueExpr(f);
        })
        .join(",\n"),
    sortExprsFn: (defaultSort) => {
      const s = viewProps.search;
      const orderBy = s.sort?.length ? `${buildDefaultOrderBy(s)},` : "";
      return `${orderBy} ${defaultSort}`;
    },
  });

  const rowGroupingData = useMemo(() => {
    if (tableGrouping === GROUP_BY_NONE_VALUE) {
      return null;
    }

    return {
      groupRows: [],
      groupBy: tableGrouping,
    };
  }, [tableGrouping]);

  const datasetRowEvents = useProjectRowEvents({
    entity: "dataset",
    entityNameProp: "name",
    orgName,
    projectName,
  });

  const runDatasetsAISearch = (openAIOpts: ClientOptions, query: string) =>
    runAISearch({
      searchType: "projectDatasets",
      apiUrl: org.api_url,
      openAIOpts,
      orgName,
      query,
      aiSchemaColumns: [],
    });

  const formatterMap: FormatterMap = useMemo(() => {
    const isGrouping = tableGrouping !== GROUP_BY_NONE_VALUE;
    return makeFormatterMap({
      name: {
        ...GroupKeyFormatterWithCell({
          Formatter: DefaultFormatter,
          groupKey: "name",
        }),
        ignoreMultilineRendering: true,
        pinnedColumnIndex: isGrouping ? 1 : undefined,
      },
      num_examples: DefaultWithAggregationFormatter({
        groupAggregationTypes,
        setGroupAggregationType,
        isGrouping,
      }),
      last_updated: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        cell: (props: FormatterProps<{ [BT_IS_GROUP]?: boolean }, any>) => (
          <DefaultFormatter
            {...props}
            className={cn({
              "font-medium px-3": props.cell.row.original[BT_IS_GROUP],
            })}
          />
        ),
      },
    });
  }, [groupAggregationTypes, setGroupAggregationType, tableGrouping]);

  const neverVisibleColumns = useMemo(() => {
    return new Set(["created"]);
  }, []);

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  return (
    <ProjectListLayout
      active="datasets"
      orgName={orgName}
      projectName={projectName}
      scrollContainerRef={scrollContainerRef}
    >
      <AppliedFilters
        className="pt-2"
        search={viewProps.search}
        clauseChecker={clauseChecker}
        setSearch={viewProps.setSearch}
        runAISearch={runDatasetsAISearch}
        fromClause={`datasets(${singleQuote(projectId)})`}
        disableFullBtqlQueryHint
      />
      <VizQuery
        query={groupQuery ?? datasetsQuery}
        signals={datasetsSignals}
        className="pt-3"
        scrollContainerRef={scrollContainerRef}
        onNumRowsChange={(numRows) => {
          numRowsRef.current = numRows;
        }}
        formatters={formatterMap}
        runAISearch={runDatasetsAISearch}
        extraLeftControls={
          <>
            <Button
              variant="primary"
              onClick={() => {
                openCreateDatasetDialog(`Dataset ${numRowsRef.current + 1}`);
              }}
              size="xs"
              Icon={Plus}
            >
              Dataset
            </Button>
            <Views
              pageIdentifier={pageIdentifier}
              viewParams={viewParams}
              viewProps={viewProps}
            />
          </>
        }
        viewProps={viewProps}
        rowSelection={rowSelectionDatasets}
        setRowSelection={setRowSelectionDatasets}
        tableRef={tableRefDatasets}
        typeHints={DuckDBTypeHints.dataset}
        initiallyVisibleColumns={{
          id: false,
        }}
        neverVisibleColumns={neverVisibleColumns}
        sizeConstraintsMap={sizeConstraintsMap}
        tableType="list"
        toolbarSlot={
          selectedRowsNumberDatasets > 0 ? (
            <>
              <CancelSelectionButton
                onCancelSelection={deselectAllTableRowsDatasets}
                selectedRowsNumber={selectedRowsNumberDatasets}
              />
              <SelectionBarButton
                onClick={() => {
                  deleteDatasetsActions.deleteEntities({
                    entityIds: getSelectedRowsWithDataDatasets().map(
                      (row) => row.id,
                    ),
                  });
                }}
                Icon={Trash}
              />
            </>
          ) : undefined
        }
        extraRightControls={selectComponent}
        rowEvents={datasetRowEvents}
        hasNoRowsComponent={
          <TableEmptyState
            Icon={Database}
            className="mt-3"
            labelClassName="text-sm leading-normal"
            label={
              <>
                <span className="mb-3 block text-base">
                  There are no datasets in this project yet.
                </span>
                Datasets can be used to store evaluation test cases, log
                generations to assess quality manually or using model graded
                evals, or store user reviewed generations to find new test
                cases.
              </>
            }
          >
            <Button
              onClick={(_e) => {
                openCreateDatasetDialog("Dataset 1");
              }}
            >
              Create dataset
            </Button>
          </TableEmptyState>
        }
        rowGroupingData={rowGroupingData}
      />
      {createDatasetModal}
      {deleteDatasetsModal}
    </ProjectListLayout>
  );
}

function useRecentDatasets() {
  const { projectDatasets } = useContext(ProjectContext);

  // Only fetch detailed experiment data for the most recent 1000 experiments.
  const datasetIds = useMemo(
    () => getRecentMetadataEntries(projectDatasets ?? []),
    [projectDatasets],
  );
  return { datasetIds };
}
