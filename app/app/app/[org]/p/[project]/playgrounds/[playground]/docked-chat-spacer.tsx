import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";
import { cn } from "#/utils/classnames";
import { motion } from "motion/react";

export const DockedChatSpacer = () => {
  const { isDocked, isChatOpen, screenTooNarrow } = useGlobalChat();

  return (
    <motion.div
      layout
      className={cn("flex-none w-0 transition-all", {
        "w-[400px]": isDocked && isChatOpen && !screenTooNarrow,
      })}
    />
  );
};
