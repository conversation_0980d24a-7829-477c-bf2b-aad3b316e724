"use client";

import { useAnalytics } from "#/ui/use-analytics";
import { useQueryFunc } from "#/utils/react-query";
import { useOrg } from "#/utils/user";
import { useCallback, useMemo, useRef, useState } from "react";
import { BodyWrapper } from "../../body-wrapper";
import { CheckOrg } from "../clientlayout";
import { type getProjectSummary, type ProjectSummary } from "../org-actions";
import { useIsClient } from "#/utils/use-is-client";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { QueryResults } from "./results";
import { BtqlEditor } from "./btql-editor";
import { type TextEditorHandle } from "#/ui/text-editor";
import { ErrorBanner } from "#/ui/error-banner";
import { type BTQLResponse } from "#/utils/btql/btql";
import { BtqlRunButton } from "./btql-run-button";
import { CopyBtqlCodeSnippet } from "./btql-code-snippet";
import { cn } from "#/utils/classnames";

// TODO:
// - Use inference queries to autocomplete columns.
// - Fill in code snippets.
// - Include and link to in docs
// - Support scrolling through with cursors.
// - Show the query plan
//
// Crazier ideas:
// - Go from a slow query in the UI into the editor easily.
// - "Paste" a BTQL curl command and view it in the editor.
// - Would be kind of magical to make real-time work

export interface Params {
  org: string;
}

export default function ClientPage({
  projectSummary: projectSummaryServer,
}: {
  params: Params;
  projectSummary: ProjectSummary[];
}) {
  const { name: orgName } = useOrg();

  useAnalytics({
    page: {
      category: "btql",
    },
  });

  const { data: projects, isPending: projectsPending } = useQueryFunc<
    typeof getProjectSummary
  >({
    fName: "getProjectSummary",
    args: { org_name: orgName },
    serverData: projectSummaryServer,
  });

  const isClient = useIsClient();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const runButtonRef = useRef<HTMLButtonElement>(null);

  const [editorValue, setEditorValue] = useEntityStorage({
    entityType: "btql",
    entityIdentifier: "editorValue",
    key: "editorValue",
    defaultValue: "",
  });

  const textEditorRef = useRef<TextEditorHandle | null>(null);
  const getCurrentQuery = useCallback(
    () => textEditorRef.current?.getValue(),
    [textEditorRef],
  );

  const [rawResult, setRawResult] = useState<BTQLResponse<
    Record<string, unknown>
  > | null>(null);

  const lintErrors = useMemo(() => {
    if (!editorValue || !rawResult || rawResult.data.length === 0) return [];
    // eslint-disable-next-line react-compiler/react-compiler
    return textEditorRef?.current?.getLintErrors() ?? [];
  }, [textEditorRef, editorValue, rawResult]);

  const [error, setError] = useState<string | null>(null);
  const [lastQueryMs, setLastQueryMs] = useState<number | null>(null);

  if (!projects && projectsPending) {
    return <TableSkeleton />;
  }

  if (!projects) {
    return <CheckOrg params={{ org: orgName }}>{null}</CheckOrg>;
  }

  if (!isClient) {
    return <TableSkeleton />;
  }

  return (
    <CheckOrg params={{ org: orgName }}>
      <BodyWrapper innerClassName="flex">
        <div
          ref={scrollContainerRef}
          className={cn("flex-1 flex-col overflow-auto px-3", {
            "pb-6": rawResult && rawResult.data.length > 0,
          })}
        >
          <div
            className={cn(
              "sticky -left-3 -mx-3 -mt-3 flex h-1/2 flex-col border-b pt-3 bg-primary-100 transition-all",
              {
                "h-[calc(100vh-145px)]":
                  !rawResult || rawResult.data.length === 0,
              },
            )}
          >
            <BtqlEditor
              mode="query"
              className="flex-1 overflow-auto p-3"
              value={editorValue}
              onDebouncedSave={setEditorValue}
              onMetaEnter={() => runButtonRef.current?.click()}
              projectData={projects}
              textEditorRef={textEditorRef}
            />
            <div className="absolute bottom-3 right-3 flex gap-2">
              <CopyBtqlCodeSnippet value={editorValue} />
              <BtqlRunButton
                getCurrentQuery={getCurrentQuery}
                runButtonRef={runButtonRef}
                setRawResult={setRawResult}
                setError={setError}
                setLastQueryMs={setLastQueryMs}
                value={editorValue}
              />
            </div>
          </div>
          {lintErrors.length > 0 && (
            <ErrorBanner skipErrorReporting className="sticky left-0 flex-none">
              {lintErrors.map((error) => (
                <div key={error.message}>{error.message}</div>
              ))}
            </ErrorBanner>
          )}
          <QueryResults
            scrollContainerRef={scrollContainerRef}
            rawResult={rawResult}
            error={error}
            lastQueryMs={lastQueryMs}
          />
        </div>
      </BodyWrapper>
    </CheckOrg>
  );
}
