import SessionRoot from "#/ui/root";
import { redirect } from "next/navigation";
import { getOrgLink } from "./[org]/getOrgLink";
import { ClientPage } from "./clientpage";
import { getToplevelOrganizationInfo } from "./actions";
import { cookies } from "next/headers";
import { LAST_VISITED_ORG_COOKIE } from "#/utils/last-visited-org";

export default async function Page() {
  const orgInfos = await getToplevelOrganizationInfo({});
  if (orgInfos && orgInfos.length === 0) {
    redirect("/app/setup");
  } else if (orgInfos && orgInfos.length >= 1) {
    // Check for last visited org in cookie and prefer it if it exists and is valid
    const cookieStore = await cookies();
    const lastVisitedOrgCookie = cookieStore.get(LAST_VISITED_ORG_COOKIE);
    const lastVisitedOrg = lastVisitedOrgCookie?.value || null;
    const targetOrg =
      orgInfos.find((org) => org.name === lastVisitedOrg) ?? orgInfos[0];

    redirect(
      getOrgLink({
        orgName: targetOrg.name,
      }),
    );
  }

  return (
    <SessionRoot loginRequired>
      <ClientPage orgInfos={orgInfos} />
    </SessionRoot>
  );
}
