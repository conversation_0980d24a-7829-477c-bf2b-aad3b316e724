![ci](https://github.com/braintrustdata/braintrust/actions/workflows/build-and-test.yaml/badge.svg)
![docker](https://github.com/braintrustdata/braintrust/actions/workflows/deploy-docker.yaml/badge.svg)

# Local development

## Setup repository

```sh
<NAME_EMAIL>:braintrustdata/braintrust.git
cd braintrust && git submodule update --init
```

To make operations with the submodules seamless, we recommend adding the
following stanza to your personal `~/.gitconfig` file.

```
[url "ssh://**************/"]
    insteadOf = https://github.com
```

## Setup dependencies

Our dev databases run in Docker Desktop. Follow the Docker Desktop [installation instructions](https://docs.docker.com/desktop/). We must use Docker Desktop.

We use [mise](https://mise.jdx.dev/) to install system tools, run small tasks
and configure the environment. [Install mise](https://mise.jdx.dev/getting-started.html) and [activate it](https://mise.jdx.dev/getting-started.html#activate-mise) in your shell. Run:

```
cd $your_code/braintrustdata/braintrust
mise trust
mise install
# You have to activate mise to run the tools it installs. Read the mise docs
# linked above to learn how to add this to your shell config.
```

Mise is configured with `mise.toml` and installs packages listed in `.tool_versions`. Env vars like `BRAINTRUST_API_KEY` should be stored in `.env` and
they will be loaded when you `cd` to the repo.

## Setup DBs

Ensure that Docker is running and then run the following commands:

```sh
make develop
make services-docker-compose    # run the databases
make migrate-dbs
```

Read more about [migrations here](#migrations-and-db-schema).

## Run Braintrust Services

```sh
make services
```

> Note: you may need to run `pnpm install` within `proxy` folder if you get an error about `NOOP_METER_PROVIDER` is not defined.

This should start up all necessary braintrust services.

### Running web standalone

You can use `pnpm run dev:web` to run the web app attached to your terminal for easier debugging.

### Debugging setup

Logs are saved to `braintrust/services/data/bt-logs`. If you run into problems during setup, these can be useful in debugging.

### VSCode / Cursor

You can attach to running `api-ts` and `test-proxy` debug sessions.

1. Start a debug session by running `DEBUG=1 cd api-ts && pnpm dev` and `DEBUG=1 cd api-ts && pnpm test-proxy`.
2. May need to stop the running services with `./services/bt_services.py stop --include api-ts test-proxy`.
3. Open VSCode Debug view and select the "Attach to (api-ts, test-proxy, or data plane)" configuration.

## Migrations and DB schema

If this is your first time setting up braintrust (or any time we make changes to
the [webapp migrations](app/supabase/migrations) or [API
migrations](api-schema/migrations)), you will need to run the corresponding
migration scripts.

If you are catching up and want to migrate all of the databases, run:

```
make migrate-dbs
```

For the webapp DB:

```sh
cd app && npx supabase migration up
```

For the API DB:

```sh
python api-schema/lambda_function.py --execute --run-all-in-foreground
```

If you for whatever reason are in a weird state where the migrations haven't applied properly, you can run `generate-migration api` which will list out a set of commands to run on your local logs postgres instance.

You can also run `generate-migration app` to run on your local app database instance.

If the output of `generate-migration app` generates foreign key constraint updates on all tables it is likely you
had a `bt_unittest` run which did not successfully clean up it's schema changes. Run

```sh
app-db-update-foreign-keys-disable-cascade-delete
```

to fix the state and run `generate-migration app` again.

### Creating a new migration

There are 2 ways you can create a new migration:

1. Manually write a migration file and replicate the changes to the final schema
2. Edit the final schema and attempt to auto-generate a migration file

These are roughly equivalent so choose whichever one makes the most sense for your use case but be sure to inspect your
migration file and `schema.sql` for correctness in either case.

Generate an empty migration file by running:

```sh
cd app && npx supabase migration new <migration-name>
```

#### Manually writing a migration

You can test locally by `npx supabase migration up`. If you need to, you can delete the row from `supabase_migrations` (the last row) table and run `up` once more (likely you'll want to comment out the alter statements).

When you've confirmed things are working make sure to update the [`schema.sql`](app/supabase/schema.sql). `schema.sql` represents the end state of the database schema so if your migration file is altering or replacing an existing table, function, etc be sure to replace the old definition in `schema.sql` entirely instead of appending an `ALTER` or `REPLACE` at the end of the file.

#### Editing `schema.sql` and auto-generating a migration

Make your desired changes to the "end state" `schema.sql` file. Once your changes are ready, you can run

```sh
generate-migration app > app/supabase/<your migration file>.sql
```

Be sure to inspect the generated migration and make any changes necessary for correctness.

Send the PR to review, and our CI/CD will need some hand holding as explained in our [migrations playbook](https://www.notion.so/Playbook-for-merging-Supabase-Migrations-dffc10b425a84ca5bb664e9bb85bf41c).

## Managing services

There are several scripts run in `make services` to launch all braintrust
services:

- `docker compose -f services/docker-compose.yml`: This compose file controls
  certain braintrust docker containers used in the local deployment.

- `./services/bt_services.py`: This script manages the remaining braintrust
  applications used in the local deployment. The script can be used to
  start/stop the applications and inspect their status. See the `SERVICES`
  variable definition in the script for more details about how each individual
  application is built and launched.

You can also run `make services-down` to stop all services.

### Hot reloading

The Next app and backend services managed by `bt_services.py` are set up for hot
reloading upon code changes. Some changes may not be picked up: for example,
changes to npm dependencies. In this case, manually rebuild with
`./services/bt_services.py restart`.

### Logging into the app.

If this is your first time running braintrust, you will need to seed the webapp
DB with a default `braintrustdata.com` organization.

```sh
./scripts/run_sql_files.py app/supabase/seed.sql
```

Open http://localhost:3000/app and sign in with test credentials: `<EMAIL>` /
`ASDFasdf1!`. This will create a user record in supabase.

## Environment variables

There are a few environment variables used by the Braintrust SDKs to interact
with the application. You probably want to have them exported before running any
scripts:

- `BRAINTRUST_APP_URL`: This is the URL of the web application. Locally it
  should be set to `http://localhost:3000`.

- `BRAINTRUST_API_KEY`: This is an API key used to authenticate yourself to
  Braintrust. You can visit the [settings
  page](http://localhost:3000/app/braintrustdata.com/settings/api-keys) to
  generate an API key, and set it as the value of `BRAINTRUST_API_KEY`.

You can use `direnv` to automatically export/unset your environment variables. Create a root `.env`, for example.

## Run unit tests

You can run the unit tests from the braintrust root directory with `make test`. The unittest runner can be
invoked with `pytest` or `python -m bt_unittest`. Pass `--help` to get a list of
configuration options.

### Unit test isolation

Unit tests are run against the local Braintrust instance in an isolated per-test
organization which is cleaned up after the test completes. This makes it
slightly tricky to poke around a failing test using the product itself. One way
to work around this is to add a `breakpoint()` line before the test exits (you
will also want to run the unit tests with `--serial` so they run in the main
process). The interpreter should stop within the test, and you can look at the
data in `self.org_name` with API key `self.org_api_key`.

### Breakpoint

You can use `--pdb` to automatically break into the debugger when a test fails.

### Expect tests

A number of our unit tests are "expect tests", which don't have explicit
pass/fail criterion, but instead capture the result of running a braintrust
program on the DB and check that nothing has changed. The tests are defined in
`tests/expect_tests/[test_name]`, and their expected outputs are saved in
`tests/expect_tests/[test_name].expected.json`.

To add a new expect test, just add your program to `tests/expect_tests`. You
will need to run the unit test runner with `--update` in order to save the
expected output:

```
# If you are running a test which uses OpenAI, make sure you have the
# OPENAI_API_KEY env var set.
python -m bt_unittest test_expect --update --expect-test-filter tests/expect_tests/[your_new_test_file]
```

### Telemetry tests

Our self-serve pricing model works by emitting telemetry events as the user uses our product. The telemetry tests reuse the expect tests to generate telemetry events. The tests are a declarative list of events that we expect to see. If you don't include an event, we will not assert that it was emitted.

#### Creating a new telemetry test

1. **Clear telemetry events**:

   - Run the following command to clear any existing telemetry events:
     ```sh
     curl -X DELETE http://localhost:3000/events
     ```

2. **Run the expect test**:

   - Execute the expect test using one of the following commands:
     ```sh
     pnpm tsx "the_expect_test.ts"
     python "the_expect_test.py"
     braintrust eval {the_expect_test.eval.[py,ts]}
     ```

3. **Get and convert events**:

   - Retrieve the events and dedupe for any extra events (it's possible to get multiple events for updates):
     ```sh
     curl -s localhost:8001/events | python -c "import json; from tests.helpers.telemetry import dedupe_events; print(dedupe_events(json.loads(input())))"
     ```

4. **Convert events into a telemetry test**:

   - If everything looks good, run the following command to convert the events into a telemetry test:
     ```sh
     curl -s localhost:8001/events | python -c "import json; from tests.helpers.telemetry import to_expected_from_events; print(to_expected_from_events(json.loads(input())))"
     ```

5. **Create a new telemetry test file**:

   - Append `.telemetry.py` to based on the file name of the expect file you used.

6. **Copy converted events**:

   - Copy and paste the converted events into the new telemetry test file.
   - Make changes as needed. You can, for example, exclude any number of properties. We'll only assert on data that you want to test against. You should use the test helpers like `number()`, `prop()`, `idempotency()`, ([source](tests/helpers/telemetry.py)) etc.

7. **Confirm the test case passes**:
   - Run the expect test to ensure the automatically converted test case passes.
     ```sh
     python -m bt_unittest the_expect_test
     ```

### Vitest Snapshots

`api-ts` also uses [vitest snapshots][]. To update, run:

```
(cd api-ts && pnpm run test --update)

```

[vitest snapshots]: https://vitest.dev/guide/snapshot.html

### Playwright integration

To run playwright tests, first install the browsers

```
cd app; npx playwright install
```

```
cd app
pnpm test-playwright [--debug] [path/to/playwright/files]
```

NOTE: many of our playwright tests create Evals during the tests, so make sure
you have `BRAINTRUST_APP_URL` and `BRAINTRUST_API_KEY` set

### Tailing development logs

To tail development logs in your terminal, run a command of the following template:

```
tail -f $(./services/bt_services.py logfile ${service_name})
```

For example, to tail the Next.js development server logs, run the following:

```
tail -f $(./services/bt_services.py logfile webapp)
```

If you prefer, it's also possible to run the Next.js development server directly
from your terminal.

From the root of this repository, run:

```
./services/bt_services.py stop --include webapp
cd app
pnpm dev
```

You can also use the `pnpm run dev:web`.

### Analyze bundles

To analyze bundle sizes

```
cd app
pnpm analyze-build
```

# Running example

## Demo using OpenAI

In a new terminal window, clone
[bt-demo](https://github.com/braintrustdata/bt-demo):

```sh
cd ..
<NAME_EMAIL>:braintrustdata/bt-demo.git
cd bt-demo
```

Create a python virtual env for bt-demo (or use direnv):

```sh
python -m venv ./venv
source venv/bin/activate
```

Install dependencies:

```sh
pip install -r requirements.txt
pip install -e ../braintrust/sdk/py
```

Go to http://localhost:3000/app/braintrustdata.com/settings/api-keys to copy or
create a new API key.

Run the demo:

```sh
BRAINTRUST_APP_URL=http://localhost:3000 BRAINTRUST_API_KEY=YOUR_API_KEY python3 ./evaluate.py
```

Now if you navigate to http://localhost:3000/app/braintrustdata.com/p/text2sql,
you should see the results of the experiment.

## Demo using expect tests

You can also use expect tests to populate your local instance with data. For
example:

```sh
BRAINTRUST_API_KEY=YOUR_API_KEY BRAINTRUST_APP_URL=http://localhost:3000 npx tsx tests/expect_tests/basic.eval.ts
```

## Email development

We use [Resend](https://resend.com/) to send transactional emails. The code for the emails can be found in the `app/ui/email-templates` directory. To edit an existing email or to create a new email, run this command from the `app/` directory:

```sh
pnpm run dev:email
```

Then, load `http://localhost:3333` to get a live preview of your email changes as you develop.

To test sending emails, ask on Slack to be added to Resend. Once in Resend, create a new Resend API Key. Then, set the `RESEND_API_KEY` environment variable in your local environment.
