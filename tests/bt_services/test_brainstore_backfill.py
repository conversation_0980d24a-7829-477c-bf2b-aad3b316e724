import time
import unittest

import braintrust
from braintrust import init
from braintrust.logger import init_dataset, init_logger
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class BrainstoreBackfillTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        # All of these tests depend on brainstore being set up.
        if BraintrustAppTestBase.skip_brainstore():
            raise unittest.SkipTest("")

    def test_backfill_sanity(self):
        # Do this twice, so that we trigger the two kinds of backfills
        # (historical and realtime).
        for i in range(2):
            # Create an experiment and wait for backfilling to catch up.
            experiment = init(project="brainstore_backfill")
            for i in range(10):
                experiment.log(id=f"{i}", input=i, output=i, scores={"foo": 0})
            experiment.flush()
            self.catchupBrainstore()

            # First, make sure that the object is tracked
            status = self.run_request(
                "get", f"{LOCAL_API_URL}/brainstore/backfill/status/project/{experiment.project.id}"
            ).json()
            object_status = next(s for s in status["object_statuses"] if s["object_type"] == "experiment")
            self.assertEqual(object_status["enabled"], True)

            # Query it and make sure that the data is there.
            data = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json={"query": f"select: * | from: experiment('{experiment.id}')"},
            ).json()
            self.assertEqual(len(data["data"]), 10)

            # Add a few new rows, and flush
            for i in range(10):
                experiment.log(input=i, output=i, scores={"foo": 0})
            experiment.flush()

            self.catchupBrainstore()

            data = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json={"query": f"select: * | from: experiment('{experiment.id}')", **self.brainstore_query_args()},
            ).json()
            self.assertEqual(len(data["data"]), 20)

    def test_delete_tracking(self):
        backfill_status = self.run_request(
            "get",
            f"{LOCAL_API_URL}/brainstore/backfill/status",
        ).json()
        if backfill_status["backfill_mode"] == "historical_full":
            raise unittest.SkipTest("Skipping delete object test because historical full backfill is enabled")

        NUM_EXPERIMENTS = 5
        NUM_DATASETS = 3

        experiment_ids = []
        for i in range(NUM_EXPERIMENTS):
            experiment = init(project="brainstore_backfill")
            experiment.log(input=0, output=0, scores={"foo": 0})
            experiment.flush()
            experiment_ids.append(experiment.id)

        dataset_ids = []
        for i in range(NUM_DATASETS):
            dataset = init_dataset(project="brainstore_backfill", name=f"dataset_{i}")
            dataset.insert(input=i, expected=i)
            dataset.flush()
            dataset_ids.append(dataset.id)

        logger = init_logger(project="brainstore_backfill")
        logger.log(input=0, output=0, scores={"foo": 0})
        logger.flush()

        def get_num_rows(object_type, object_id):
            data = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json={
                    "query": f"select: * | from: {object_type}('{object_id}')",
                    **self.brainstore_query_args(realtime=True),
                },
            ).json()
            return len(data["data"])

        for experiment_id in experiment_ids:
            self.assertEqual(get_num_rows("experiment", experiment_id), 1)
        for dataset_id in dataset_ids:
            self.assertEqual(get_num_rows("dataset", dataset_id), 1)
        self.assertEqual(get_num_rows("project_logs", logger.id), 1)

        # Delete the backfilled stuff. We need to make sure we're caught up on
        # backfilling so that we have tracked all object IDs we have written to.
        self.catchupBrainstore()
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/brainstore/backfill/delete",
            json={"project_id": logger.project.id},
        )

        for experiment_id in experiment_ids:
            self.assertEqual(get_num_rows("experiment", experiment_id), 0)
        for dataset_id in dataset_ids:
            self.assertEqual(get_num_rows("dataset", dataset_id), 0)
        self.assertEqual(get_num_rows("project_logs", logger.id), 0)

    def test_brainstore_realtime_root_replace(self):
        # NOTE: To properly test this locally, you should disable explicit compaction. Otherwise it doesn't
        # always repro.
        logger = init_logger(project="brainstore_backfill")

        with logger.start_span(name="test", id="1") as span:
            span.log(input="root0")
            with span.start_span(name="child") as child:
                child.log(input="child0")
        logger.flush()

        # Now, recreate the same span but with a different child span
        with logger.start_span(name="test", id="1") as span:
            span.log(input="root1")
            with span.start_span(name="child") as child:
                child.log(input="child1")
        logger.flush()

        # Now, fetch the data with id=1 and validate that the correct span is returned
        data = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"select: * | from: project_logs('{logger.id}') traces | filter: id='1'",
                **self.brainstore_query_args(),
            },
        ).json()
        self.assertEqual(len(data["data"]), 2)
        root = [x for x in data["data"] if x["is_root"]]
        child = [x for x in data["data"] if not x["is_root"]]
        self.assertEqual(len(root), 1)
        self.assertEqual(len(child), 1)
        self.assertEqual(root[0]["input"], "root1")
        self.assertEqual(child[0]["input"], "child1")

    @parameterized.expand(
        [
            ("spans",),
            ("traces",),
            ("summary",),
        ]
    )
    def test_realtime_cursor(self, shape):
        logger = init_logger(project="brainstore_backfill_realtime")

        # This is best to test with compaction disabled, but it should repro even without it, because we query so quickly.
        for i in range(100):
            logger.log(input=i, output=i, scores={"foo": 0})
        logger.flush()

        # Query it through brainstore, and make sure that the data is there.
        cursor = None
        iters = 0
        for i in range(15):
            data = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json={
                    "query": f"""select: * | from: project_logs('{logger.id}') {shape} | sort: _pagination_key DESC | limit: 10 {f' | cursor: "{cursor}"' if cursor else ''}""",
                    **self.brainstore_query_args(),
                },
            ).json()

            if len(data["data"]) == 0:
                break

            cursor = data.get("cursor")
            self.assertIsNotNone(cursor)

            iters += 1
        self.assertEqual(iters, 10)

    def test_query_timeout(self):
        logger = init_logger(project="brainstore_backfill_query_timeout")
        for i in range(5):
            logger.log(input=i, output=i, scores={"foo": 0})
        logger.flush()

        data = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""select: * | from: project_logs('{logger.id}') | sort: _pagination_key DESC | limit: 10""",
                "query_timeout_seconds": 0,
                **self.brainstore_query_args(),
            },
            expect_error=True,
            allow_500_errors=True,
        )
        message = data.json()["Message"]
        self.assertIn("Timeout", message)

    def test_backfill_default_traces(self):
        logger = init_logger(project="brainstore_backfill_default_traces")
        with logger.start_span(name="test", id="1") as span:
            span.log(input="root0")
            with span.start_span(name="child", id="2") as child:
                child.log(input="child0")
        logger.flush()

        # Don't specify anything
        data = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"select: * | from: project_logs('{logger.id}') | filter: id='1'",
                "brainstore_realtime": True,
            },
        ).json()

        # We should get just the one matching span because it defaults to spans.
        self.assertEqual(len(data["data"]), 1)
        self.assertIsNotNone(data["data"][0]["_pagination_key"])  # Make sure it came from Brainstore

        # Now specify brainstore explicitly
        data = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"select: * | from: project_logs('{logger.id}') | filter: id='1'",
                "use_brainstore": True,
                "brainstore_realtime": True,
            },
        ).json()

        # We should get just the one matching span
        self.assertEqual(len(data["data"]), 1)
        self.assertIsNotNone(data["data"][0]["_pagination_key"])  # Make sure it came from Brainstore
