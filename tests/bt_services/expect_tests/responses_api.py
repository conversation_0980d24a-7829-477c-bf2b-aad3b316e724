import asyncio

import braintrust
from openai import AsyncOpenAI, OpenAI, pydantic_function_tool
from pydantic import BaseModel

logger = braintrust.init_logger("openai responses")

ARGS = dict(
    model="gpt-4o",
    input=[{"role": "system", "content": "say 10"}],
    text={
        "format": {
            "type": "json_schema",
            "name": "calendar_event",
            "schema": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "date": {"type": "string"},
                    "participants": {"type": "array", "items": {"type": "string"}},
                },
                "required": ["name", "date", "participants"],
                "additionalProperties": False,
            },
            "strict": True,
        }
    },
)


class QueryCalendarEvent(BaseModel):
    name: str
    date: str


class CalendarEvent(BaseModel):
    name: str
    date: str
    participants: list[str]


TOOLS = [QueryCalendarEvent]


def test_sync():
    client = braintrust.wrap_openai(OpenAI())
    print(client.responses.create(**ARGS))

    res = client.beta.chat.completions.parse(
        model="gpt-4o",
        messages=[{"role": "system", "content": "say 10"}],
        response_format=CalendarEvent,
        tools=[pydantic_function_tool(tool) for tool in TOOLS],
        temperature=0.0,
    )
    print(res)


async def test_async():
    async_client = braintrust.wrap_openai(AsyncOpenAI())
    print(await async_client.responses.create(**ARGS))


test_sync()
asyncio.run(test_async())
braintrust.flush()
